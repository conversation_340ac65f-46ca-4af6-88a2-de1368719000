"""
公司数据服务
"""
from typing import Dict, Any

import httpx

from app.core.config import settings
from app.core.exceptions import FetcherError
from app.utils.cache import redis_cache


class CompanyDataService:
    """公司数据服务类"""
    
    def __init__(self):
        self.base_url = settings.COMPANY_API_BASE_URL
        self.key_897 = settings.COMPANY_API_KEY_897  # 公司基本信息
        self.key_945 = settings.COMPANY_API_KEY_945  # 专利列表
        self.key_943 = settings.COMPANY_API_KEY_943  # 专利详情

    async def _make_request(self, endpoint: str, params: Dict[str, Any], api_key: str) -> Dict[str, Any]:
        """发送 GET 请求的通用方法"""
        url = f"{self.base_url}/{endpoint}"
        params_with_key = {"key": api_key, **params}
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params_with_key, timeout=10.0)
                response.raise_for_status()
                response_data = response.json()

                # 检查 API 返回的业务错误 (根据示例，成功时 code 为 '0')
                if str(response_data.get("code")) != '0':
                    raise FetcherError(f"外部 API 返回业务错误: {response_data.get('msg')}")

                return response_data

        except httpx.RequestError as e:
            raise FetcherError(f"请求外部 API 失败: {e}") from e

    @redis_cache(key_template="company_info:{keyword}")
    async def get_company_info(self, keyword: str) -> Dict[str, Any]:
        """获取公司基本信息 (对应 economic/897)"""
        response = await self._make_request("897", {"keyword": keyword}, self.key_897)
        return response.get("data", {})

    async def get_patent_list(self, keyword: str, page_num: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """获取公司的专利列表信息 (对应 economic/945)"""
        params = {
            "keyword": keyword,
            "pageNum": page_num,
            "pageSize": page_size,
        }
        return await self._make_request("945", params, self.key_945)

    async def get_patent_detail(self, application_number: str) -> Dict[str, Any]:
        """根据申请号获取单个专利的详细信息 (对应 economic/943)"""
        return await self._make_request("943", {"applicationNumber": application_number}, self.key_943)


# 全局服务实例
company_service = CompanyDataService()


def get_company_service() -> CompanyDataService:
    """获取公司数据服务实例"""
    return company_service

# 应用基本配置
APP_NAME=标讯数据接口
APP_VERSION=2.0.0
APP_DESCRIPTION=一个基于 FastAPI 的高可扩展性标讯数据接口服务
DEBUG=false

# 服务器配置
HOST=127.0.0.1
PORT=8000

# 建安大数据API配置
BID_API_APPID=sfNRBYRAEFBAABBQYBCSkf
BID_API_KEY=482AlQr1
BID_API_URL_LIST=https://api.jianyu360.com/data/biddata/list
BID_API_URL_INFO=https://api.jianyu360.com/data/biddata/info

# 厦门市信用综合服务平台API配置
COMPANY_API_BASE_URL=https://api.xmcic.cn:8443/government/economic
COMPANY_API_KEY_897=f90393700d1b215bd861da174b7a396d
COMPANY_API_KEY_945=ad2022ced062df34ca3302617ffafb26
COMPANY_API_KEY_943=51df265a42c636f3df28004fccc8dca1

# HiAgent配置
HIAGENT_ENDPOINT=http://**************/api/proxy/api/v1
HIAGENT_QUERY_KEY=d199pbnr8lobce3ri730
HIAGENT_PROBABILITY_KEY=d19uginr8lobce3ric0g
HIAGENT_KEY_FACTOR_KEY=d1a5rdfr8lobce3ricj0
HIAGENT_UID=567

# CORS配置
CORS_ORIGINS=["*"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["*"]
CORS_ALLOW_HEADERS=["*"]

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
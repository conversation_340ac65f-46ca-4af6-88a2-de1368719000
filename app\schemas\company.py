"""
公司相关的Pydantic模型
"""
from typing import Optional
from pydantic import BaseModel, Field


class CompanySearchPayload(BaseModel):
    """公司搜索请求模型"""
    company_name: str = Field(..., description="公司名称")
    page: int = Field(1, description="页码")
    size: int = Field(10, description="每页数量")


class CompanyInfoResponse(BaseModel):
    """公司信息响应模型"""
    code: str
    msg: str
    data: Optional[dict] = None


class PatentListRequest(BaseModel):
    """专利列表请求模型"""
    keyword: str = Field(..., description="公司名称关键字")
    page_num: int = Field(1, description="页码")
    page_size: int = Field(20, description="每页数量")


class PatentListResponse(BaseModel):
    """专利列表响应模型"""
    code: str
    msg: str
    data: Optional[dict] = None


class PatentDetailResponse(BaseModel):
    """专利详情响应模型"""
    code: str
    msg: str
    data: Optional[dict] = None

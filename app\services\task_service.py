from redis.asyncio import Redis
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from fastapi import Depends

from app.core.scheduler import get_scheduler
from app.core.redis import get_redis_client
from app.crud.crud_task import remove_task_latest_executed, record_task_latest_executed


class TaskService:
    def __init__(self, scheduler: AsyncIOScheduler, redis_client: Redis):
        self.scheduler = scheduler
        self.redis_client = redis_client

    async def add_task(self, func: str, task_id: str, args, **kwargs) -> None:
        """
        添加一个任务到调度器中。

        :param func: 任务函数的路径
        :param task_id: 任务的唯一标识符
        :param kwargs: 任务的其他参数
        """
        self.scheduler.add_job(
            func=func,
            id=task_id,
            args=args,
            
            replace_existing=True,
            max_instances=1,
            coalesce=True,

            **kwargs
        )
        await record_task_latest_executed(task_id, self.redis_client)

    async def remove_task(self, task_id: str) -> None:
        """
        从调度器中移除一个任务。

        :param task_id: 任务的唯一标识符
        """
        if self.scheduler.get_job(task_id):
            self.scheduler.remove_job(task_id)
            await remove_task_latest_executed(task_id, self.redis_client)


def get_task_service(
        scheduler: AsyncIOScheduler = Depends(get_scheduler), 
        redis_client: Redis = Depends(get_redis_client)
) -> TaskService:
    """
    获取任务服务实例。

    :param scheduler: APScheduler 实例
    :param redis_client: Redis 客户端实例
    :return: TaskService 实例
    """
    return TaskService(scheduler=scheduler, redis_client=redis_client)
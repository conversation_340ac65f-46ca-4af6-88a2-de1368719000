from redis.asyncio import Redis
from app.crud.crud_config import get_user_config, set_user_config, init_user_config

class UserConfigService:
    def __init__(self, redis_client: Redis):
        self.redis_client = redis_client

    async def get_user_config(self, user_id: str):
        config = await get_user_config(user_id, self.redis_client)
        if not config:
            # 如果配置不存在，则初始化
            await init_user_config(user_id, self.redis_client)
            config = await get_user_config(user_id, self.redis_client)
        return config

    async def update_user_config(self, user_id: str, config_data: dict):
        updated_config = await set_user_config(user_id, config_data, self.redis_client)
        return updated_config
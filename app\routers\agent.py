"""
HiAgent智能对话相关API路由
"""
from fastapi import APIRouter, Path, Query, Depends
from typing import List, Dict

from app.schemas.agent import (
    Message,
    InquiryPayload,
    PredictionPayload,
    KeyFactorsPayload,
    QueryResponse
)
from app.services.agent_service import AgentService, get_agent_service
from app.utils.response import success_response
from app.schemas.response import ApiResponse

router = APIRouter(prefix="/agent", tags=["智能对话"])


@router.get("/{agent_name}/messages", summary="获取 HiAgent 对话消息列表", response_model=ApiResponse[List[Message]])
async def get_conversation_messages(
    agent_name: str = Path(..., description="Agent 的名称"),
    conversation_id: str = Query(..., description="对话的唯一 ID"),
    limit: int = Query(100, description="消息数量限制"),
    service: AgentService = Depends(get_agent_service)
):
    """
    获取指定 HiAgent 对话的历史消息列表。

    参数:
    - conversation_id: 对话的唯一标识符
    - limit: 返回的消息数量限制

    返回:
    - 格式化的消息列表
    """
    messages = await service.get_conversation_messages(conversation_id=conversation_id, agent_name=agent_name, limit=limit)
    return success_response(data=messages)


@router.post("/inquiry", summary="智能问询", response_model=ApiResponse[QueryResponse])
async def inquiry(
    payload: InquiryPayload,
    service: AgentService = Depends(get_agent_service)
):
    """
    向 HiAgent 发送查询，并返回回答。

    参数:
    - query: 用户提出的问题

    返回:
    - HiAgent 的回答数据
    """
    data = await service.inquiry(query=payload.query, conversation_id=payload.conversation_id)
    return success_response(data=data)


@router.post("/inquiry/create", summary="创建 Inquiry 对话", response_model=ApiResponse[str])
async def create_inquiry_conversation(
    service: AgentService = Depends(get_agent_service)
):
    """
    创建一个新的 Inquiry 对话，并返回对话 ID。

    返回:
    - 对话 ID
    """
    conversation_id = await service.create_conversation("inquiry")
    return success_response(data=conversation_id)


@router.post("/predict_probabilities", summary="预测中标概率", response_model=ApiResponse[List[Dict]])
async def predict_probabilities(
    payload: PredictionPayload,
    service: AgentService = Depends(get_agent_service)
):
    """
    根据标讯信息和公司信息，预测中标概率。

    参数:
    - bid_info: 标讯信息字典
    - company_info: 公司信息字典

    返回:
    - 中标概率预测结果
    """
    probabilities = await service.predict_probabilities(
        bid_info=payload.bid_info,
        company_info=payload.company_info
    )
    return success_response(data=probabilities)


@router.post("/extract_keyfactors", summary="获取项目关键因素", response_model=ApiResponse[List[str]])
async def extract_key_factors(
    payload: KeyFactorsPayload,
    service: AgentService = Depends(get_agent_service)
):
    """
    根据标讯信息，获取项目的关键因素。

    参数:
    - bid_info: 标讯信息字典

    返回:
    - 项目关键因素列表
    """
    key_factors = await service.extract_key_factors(bid_info=payload.bid_info)
    return success_response(data=key_factors)
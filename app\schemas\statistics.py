"""
统计分析相关的Pydantic模型
"""
from typing import List, Dict, Any
from pydantic import BaseModel, Field


class CompanyPartnersResponse(BaseModel):
    """公司合作伙伴响应模型"""
    company_name: str = Field(..., description="公司名称")
    partners: Dict[str, int] = Field(..., description="合作伙伴字典，键为公司名称，值为合作次数")


class CompanyBidStatistics(BaseModel):
    """公司投标统计模型"""
    company_name: str = Field(..., description="公司名称")
    tender_count: int = Field(..., description="投标项目数量")
    win_count: int = Field(..., description="中标项目数量")
    win_rate: float = Field(..., description="中标率（百分比）")


class CompanyIndustryStats(BaseModel):
    """公司行业统计模型"""
    company_name: str = Field(..., description="公司名称")
    as_buyer: int = Field(..., description="作为采购单位的项目数")
    as_winner: int = Field(..., description="作为中标单位的项目数")
    total_projects: int = Field(..., description="参与项目总数")


class IndustryStatistics(BaseModel):
    """行业统计模型"""
    industry: str = Field(..., description="行业分类")
    total_projects: int = Field(..., description="行业项目总数")
    companies: List[CompanyIndustryStats] = Field(..., description="参与公司列表")
    avg_budget: float = Field(..., description="平均预算金额")

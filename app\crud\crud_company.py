"""
公司信息的CRUD操作
"""
from sqlalchemy.orm import Session
from app.models import Company

def get_company_by_name(db: Session, name: str):
    """根据名称查询公司"""
    return db.query(Company).filter(Company.name == name).first()

def create_company(db: Session, company_data: dict):
    """创建新公司"""
    db_company = Company(**company_data)
    db.add(db_company)
    db.commit()
    db.refresh(db_company)
    return db_company

def get_companies(db: Session, skip: int = 0, limit: int = 10):
    """获取公司列表（分页）"""
    return db.query(Company).offset(skip).limit(limit).all()
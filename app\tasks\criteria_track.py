from datetime import datetime
from fastapi.logger import logger

from app.schemas.criteria import SearchCriteriaContent
from app.core.redis import get_redis_client
from app.core.database import SessionLocal
from app.core.scheduler import get_scheduler
from app.crud.fetch_bid import query_bids
from app.crud.crud_criteria import get_criteria_by_id
from app.crud.crud_task import record_task_latest_executed, get_task_latest_executed, get_track_criteria_task_id
from app.services.bid_service import get_bid_service
from app.services.task_service import get_task_service

async def track_criteria_task(
    user_id: str,
    criteria_id: str
) -> bool:
    """
    跟踪或取消跟踪指定的条件。
    
    参数:
    - user_id: 用户 ID
    - criteria_id: 条件 ID
    """
    task_id = get_track_criteria_task_id(user_id, criteria_id)

    db = SessionLocal()  # 假设有一个获取数据库会话的函数
    redis_client = await get_redis_client()

    scheduler = get_scheduler()
    bid_service = get_bid_service(task_service=get_task_service(scheduler, redis_client))

    criteria = get_criteria_by_id(db, criteria_id)
    criteria = SearchCriteriaContent.model_validate(criteria)
    if not criteria or not criteria.is_tracked:
        logger.error(f"条件 ID {criteria_id} 不存在或未被跟踪")
        await bid_service.task_service.remove_task(task_id)
        return False
    
    last_execute_time = await get_task_latest_executed(task_id, redis_client)
    await record_task_latest_executed(task_id, redis_client)

    criteria.publish_time_start = datetime.fromtimestamp(last_execute_time)
    criteria.publish_time_end = datetime.now()
    newly_tracked = await query_bids(criteria)
    if newly_tracked.size == 0:
        logger.info(f"没有新的标讯数据需要跟踪: {criteria_id}")
        return False

    logger.info(f"跟踪新标讯: {criteria_id}, 新标讯数量: {newly_tracked.size}, 查询条件: {criteria}")
    

    return True
"""
标讯数据服务
"""
import asyncio
import time
import httpx
import hashlib
from typing import Dict, Any, List
from logging import getLogger
from fastapi.params import Depends
from redis.asyncio import Redis
from urllib.parse import unquote

from app.core.config import settings
from app.core.exceptions import FetcherError
from app.crud.crud_bid_track import get_tracked_bid_ids, is_bid_tracked, untrack_bid, track_bid
from app.crud.crud_task import get_track_bid_task_id
from app.crud.fetch_bid import get_bid_details, query_bids
from app.schemas.bid import BidInfo, BidListResponse
from app.schemas.criteria import SearchCriteriaContent, map_criteria_to_payload
from app.services.task_service import TaskService, get_task_service
from app.utils.cache import redis_cache


logger = getLogger(__name__)


class BidDataService:
    """标讯数据服务类"""

    def __init__(self, task_service: TaskService):
        self.appid = settings.BID_API_APPID
        self.key = settings.BID_API_KEY
        self.api_url_list = settings.BID_API_URL_LIST
        self.api_url_info = settings.BID_API_URL_INFO
        self.headers = {'Content-Type': 'application/json;charset=utf-8'}
        self.task_service = task_service

    def _generate_token(self, timestamp: int) -> str:
        """根据 appid, timestamp, 和 key 生成 token"""
        string_to_encrypt = self.appid + str(timestamp) + self.key
        token = hashlib.md5(string_to_encrypt.encode('utf-8')).hexdigest().upper()
        return token

    async def _call_external_api(self, url: str, payload: Dict[str, Any]) -> Dict[str, Any]:
        """异步调用外部 API 的通用函数"""
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    url, 
                    headers=self.headers, 
                    json=payload,
                    timeout=10.0
                )
                response.raise_for_status()
            except httpx.RequestError as e:
                raise FetcherError(f"请求外部 API 失败: {e}") from e

        response_data = response.json()
        
        # 处理外部 API 返回的业务错误
        if response_data.get("code") != 0:
            raise FetcherError(f"外部 API 返回错误: {response_data.get('msg')}")
            
        return response_data

    @redis_cache(key_template="bid_list:{query_params}", ttl=60 * 30)
    async def query_bids(self, criteria: SearchCriteriaContent) -> Dict[str, Any]:
        """根据指定的条件异步查询标讯列表"""
        timestamp = int(time.time())
        token = self._generate_token(timestamp)

        query_params = map_criteria_to_payload(criteria)
        payload = query_params.model_dump(exclude_unset=True)
        payload.update({
            "appid": self.appid,
            "token": token,
            "timestamp": str(timestamp),
        })

        return await self._call_external_api(self.api_url_list, payload)

    @redis_cache(key_template="bid_details:{bid_id}", ttl=60 * 60 * 24)
    async def get_bid_details(self, bid_id: str) -> BidInfo | None:
        """根据标讯 ID 异步获取其详细信息"""
        timestamp = int(time.time())
        token = self._generate_token(timestamp)

        payload = {
            "appid": self.appid,
            "token": token,
            "timestamp": str(timestamp),
            "id": bid_id
        }
        
        resp = await self._call_external_api(self.api_url_info, payload)
        if "data" not in resp or not resp["data"]:
            logger.error(f"标讯 ID {bid_id} 的数据不存在或格式错误, 返回数据: {resp}")
            return None
        else:
            return BidInfo(**resp["data"][0])

    async def get_bid_details_with_track_status(self, bid_id: str, user_id: str, redis_client: Redis) -> BidInfo | None:
        """获取标讯详情并附带跟踪状态"""
        bid_info = await get_bid_details(bid_id)
        if not bid_info:
            return None
        
        # 获取用户跟踪状态
        is_tracked = await is_bid_tracked(user_id, bid_id, redis_client)
        bid_info.track_status = is_tracked
        
        return bid_info
    
    async def query_bids_with_track_status(
        self, 
        criteria: SearchCriteriaContent, 
        user_id: str, 
        redis_client: Redis
    ) -> BidListResponse:
        """查询标讯列表并附带跟踪状态"""
        query_resp = await query_bids(criteria)
        bids = query_resp.data or []

        # 获取用户跟踪的标讯 ID 列表, 注意 redis 返回的是 bytes
        tracked_bid_ids = await get_tracked_bid_ids(user_id, redis_client)

        for bid in bids:
            bid.track_status = unquote(bid.id) in tracked_bid_ids

        return query_resp

    async def change_bid_track_status(self, user_id: str, bid_id: str, track_status: bool, redis_client: Redis) -> bool:
        """改变标讯的跟踪状态"""
        bid_track_task_id = get_track_bid_task_id(user_id, bid_id)
        if track_status:
            await self.task_service.add_task(
                func="app.tasks.project_track:track_project_task",
                args=[user_id, bid_id],
                task_id=bid_track_task_id,
                trigger="interval",
                minutes=60,
            )
            res = await track_bid(user_id, bid_id, redis_client)
        else:
            self.task_service.remove_task(bid_track_task_id)
            res = await untrack_bid(user_id, bid_id, redis_client)
        return res > 0

    async def get_user_tracked_bids(self, user_id: str, redis_client: Redis) -> List[BidInfo]:
        """获取用户跟踪的标讯"""
        bid_ids = await get_tracked_bid_ids(user_id, redis_client)

        # 如果标讯详情接口返回空值，把它从订阅列表移除
        async def _get_bid_details(bid_id: str) -> BidInfo | None:
            """获取单个标讯详情"""
            bid_info = await get_bid_details(bid_id)
            if not bid_info:
                await untrack_bid(user_id, bid_id, redis_client)
                return None
            bid_info.track_status = True  # 设置跟踪状态为 True
            return bid_info

        tasks = [_get_bid_details(bid_id) for bid_id in bid_ids]
        bid_infos = await asyncio.gather(*tasks)
        bid_infos = [bid for bid in bid_infos if bid is not None]

        return bid_infos

    async def search_company_tenders(self, company_name: str, page: int = 1, size: int = 10) -> Dict[str, Any]:
        """搜索某公司的投标项目"""
        criteria = SearchCriteriaContent(
            keyword=company_name,
            keyword_scope="采购单位",
            page=page,
            size=size,
        )
        return await query_bids(criteria)

    async def search_company_wins(self, company_name: str, page: int = 1, size: int = 10) -> Dict[str, Any]:
        """搜索某公司的中标项目"""
        criteria = SearchCriteriaContent(
            keyword=company_name,
            keyword_scope="中标单位",
            page=page,
            size=size,
        )
        return await query_bids(criteria)


def get_bid_service(task_service: TaskService = Depends(get_task_service)) -> BidDataService:
    """获取标讯数据服务实例"""
    return BidDataService(task_service=task_service)

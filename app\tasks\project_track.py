from datetime import datetime

from fastapi.logger import logger
from app.core.scheduler import get_scheduler
from app.core.redis import get_redis_client
from app.crud.fetch_bid import query_bids, get_bid_details
from app.crud.crud_task import record_task_latest_executed, get_task_latest_executed, get_track_bid_task_id
from app.schemas.criteria import SearchCriteriaContent
from app.services.bid_service import get_bid_service
from app.services.task_service import get_task_service

async def track_project_task(
    user_id: str,
    bid_id: str,
) -> bool:
    """
    跟踪或取消跟踪指定的标讯。
    
    参数:
    - user_id: 用户 ID
    - bid_id: 标讯 ID
    - track_status: 是否跟踪标讯
    """
    task_id = get_track_bid_task_id(user_id, bid_id)

    redis_client = await get_redis_client()
    last_execute_time = await get_task_latest_executed(task_id, redis_client)
    await record_task_latest_executed(task_id, redis_client)

    scheduler = get_scheduler()
    bid_service = get_bid_service(task_service=get_task_service(scheduler, redis_client))
    bid_info = await get_bid_details(bid_id)
    if not bid_info:
        await bid_service.change_bid_track_status(user_id, bid_id, False, redis_client)
        logger.error(f"标讯 ID {bid_id} 的数据不存在或格式错误")
        return False
    
    project_no = bid_info.projectcode
    project_name = bid_info.projectname
    query_payload = SearchCriteriaContent(
        keywords=project_name,
        keyword_scope="项目名称",
        publish_time_start=datetime.fromtimestamp(last_execute_time),
        publish_time_end=datetime.now(),
    )
    newly_tracked = await query_bids(query_payload)
    if newly_tracked.size == 0:
        logger.info(f"没有新的标讯数据需要跟踪: {project_no} - {project_name}")
        return False

    logger.info(f"跟踪新标讯: {project_no} - {project_name}, 新标讯数量: {newly_tracked.size}, 查询条件: {query_payload}")

    return True
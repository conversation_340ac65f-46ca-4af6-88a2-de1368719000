from redis.asyncio import Redis
from datetime import datetime


async def record_task_latest_executed(
    task_id: str, 
    redis_client: Redis
) -> None:
    # 记录任务最近一次执行的时间
    await redis_client.set(f"task_latest_executed:{task_id}", datetime.now().timestamp())

async def get_task_latest_executed(
    task_id: str, 
    redis_client: Redis
) -> int | None:
    # 获取任务最近一次执行的时间戳
    timestamp = await redis_client.get(f"task_latest_executed:{task_id}")
    if timestamp is not None:
        return int(float(timestamp))
    return None

async def remove_task_latest_executed(
    task_id: str, 
    redis_client: Redis
) -> None:
    # 移除任务最近一次执行的记录
    await redis_client.delete(f"task_latest_executed:{task_id}")

def get_track_bid_task_id(user_id: str, bid_id: str) -> str:
    """
    生成跟踪标讯的任务 ID。
    
    参数:
    - user_id: 用户 ID
    - bid_id: 标讯 ID
    """
    return f"track_bid_{user_id}_{bid_id}"

def get_track_criteria_task_id(user_id: str, criteria_id: str) -> str:
    """
    生成跟踪条件的任务 ID。

    参数:
    - user_id: 用户 ID
    - criteria_id: 条件 ID
    """
    return f"track_criteria_{user_id}_{criteria_id}"
"""
标讯相关API路由
"""
from fastapi import APIRouter, Path, Depends, Body, Query
from typing import List, Dict, Annotated

from app.schemas.bid import BidInfo, BidListResponse
from app.schemas.criteria import SearchCriteriaContent
from app.schemas.company import CompanySearchPayload
from app.services.bid_service import BidDataService, get_bid_service
from app.utils.response import success_response
from app.schemas.response import ApiResponse
from app.core.security import get_current_user_id
from app.core.redis import get_redis_client
from redis.asyncio import Redis

router = APIRouter(prefix="/bids", tags=["标讯数据"])


@router.get("/tracked", summary="获取跟踪的标讯", response_model=ApiResponse[List[BidInfo]])
async def get_tracked_bids(
    user_id: Annotated[str, Depends(get_current_user_id)],
    service: BidDataService = Depends(get_bid_service),
    redis_client: Redis = Depends(get_redis_client),
):
    tracked_bids = await service.get_user_tracked_bids(user_id, redis_client)
    return success_response(data=tracked_bids)


@router.put("/{bid_id:path}/track", summary="更新标讯跟踪状态", response_model=ApiResponse[None])
async def update_bid_track_status(
    track_status: Annotated[bool, Query(..., description="是否跟踪")],
    bid_id: Annotated[str, Path(..., description="要跟踪的标讯的唯一 ID")],
    user_id: Annotated[str, Depends(get_current_user_id)],
    service: BidDataService = Depends(get_bid_service),
    redis_client: Redis = Depends(get_redis_client),
):
    res = await service.change_bid_track_status(user_id, bid_id, track_status, redis_client)
    return success_response()


@router.post("/query", summary="查询标讯列表", response_model=ApiResponse[BidListResponse])
async def query_bids(
    criteria: Annotated[SearchCriteriaContent, Body(..., description="查询条件")],
    user_id: Annotated[str, Depends(get_current_user_id)],
    redis_client: Annotated[Redis, Depends(get_redis_client)],
    service: Annotated[BidDataService, Depends(get_bid_service)]
):
    """
    根据指定的条件查询标讯列表。
    """
    data = await service.query_bids_with_track_status(criteria, user_id, redis_client)
    return success_response(data=data, message="查询标讯列表成功")


@router.get("/{bid_id}", summary="获取标讯详情", response_model=ApiResponse[BidInfo])
async def get_bid_details(
    bid_id: str = Path(..., description="要查询的标讯的唯一 ID"),
    user_id: str = Depends(get_current_user_id),
    redis_client: Redis = Depends(get_redis_client),
    service: BidDataService = Depends(get_bid_service)
):
    """
    根据标讯 ID 获取其详细信息。
    """
    data = await service.get_bid_details_with_track_status(bid_id, user_id=user_id, redis_client=redis_client)
    return success_response(data=data, message="获取标讯详情成功")


@router.post("/company/tenders", summary="搜索某公司的投标项目", response_model=ApiResponse[Dict])
async def search_company_tenders(
    payload: CompanySearchPayload,
    service: BidDataService = Depends(get_bid_service)
):
    """
    根据公司名称搜索其作为**投标单位**的项目。
    """
    data = await service.search_company_tenders(
        company_name=payload.company_name,
        page=payload.page,
        size=payload.size
    )
    return success_response(data=data, message="搜索公司投标项目成功")


@router.post("/company/wins", summary="搜索某公司的中标项目", response_model=ApiResponse[Dict])
async def search_company_wins(
    payload: CompanySearchPayload,
    service: BidDataService = Depends(get_bid_service)
):
    """
    根据公司名称搜索其作为**中标单位**的项目。
    """
    data = await service.search_company_wins(
        company_name=payload.company_name,
        page=payload.page,
        size=payload.size
    )
    return success_response(data=data, message="搜索公司中标项目成功")
"""
响应构建工具函数
"""
from typing import TypeVar, Optional, Any
import math

from app.schemas.response import (
    SuccessResponse,
    ErrorResponse,
    PaginatedResponse,
    PaginatedApiResponse,
    ResponseCode,
    ResponseMessage
)

T = TypeVar('T')


def success_response(
    data: Optional[T] = None,
    message: str = ResponseMessage.SUCCESS,
    code: int = ResponseCode.SUCCESS
) -> SuccessResponse[T]:
    """
    构建成功响应

    Args:
        data: 响应数据
        message: 响应消息
        code: 响应状态码

    Returns:
        SuccessResponse: 统一格式的成功响应
    """
    return SuccessResponse(code=code, message=message, data=data)


def error_response(
    message: str,
    code: int = ResponseCode.INTERNAL_ERROR,
    data: Optional[Any] = None
) -> ErrorResponse:
    """
    构建错误响应

    Args:
        message: 错误消息
        code: 错误状态码
        data: 错误详情数据

    Returns:
        ErrorResponse: 统一格式的错误响应
    """
    return ErrorResponse(code=code, message=message, data=data)


def paginated_response(
    items: list[T],
    total: int,
    page: int,
    size: int,
    message: str = ResponseMessage.SUCCESS,
    code: int = ResponseCode.SUCCESS
) -> PaginatedApiResponse[T]:
    """
    构建分页响应

    Args:
        items: 数据列表
        total: 总数量
        page: 当前页码
        size: 每页数量
        message: 响应消息
        code: 响应状态码

    Returns:
        PaginatedApiResponse: 统一格式的分页响应
    """
    pages = math.ceil(total / size) if size > 0 else 0

    paginated_data = PaginatedResponse(
        items=items,
        total=total,
        page=page,
        size=size,
        pages=pages
    )

    return PaginatedApiResponse(
        code=code,
        message=message,
        data=paginated_data
    )


def invalid_params_response(message: str = ResponseMessage.INVALID_PARAMS) -> ErrorResponse:
    """构建参数错误响应"""
    return error_response(message=message, code=ResponseCode.INVALID_PARAMS)


def not_found_response(message: str = ResponseMessage.NOT_FOUND) -> ErrorResponse:
    """构建资源不存在响应"""
    return error_response(message=message, code=ResponseCode.NOT_FOUND)


def unauthorized_response(message: str = ResponseMessage.UNAUTHORIZED) -> ErrorResponse:
    """构建未授权响应"""
    return error_response(message=message, code=ResponseCode.UNAUTHORIZED)


def forbidden_response(message: str = ResponseMessage.FORBIDDEN) -> ErrorResponse:
    """构建禁止访问响应"""
    return error_response(message=message, code=ResponseCode.FORBIDDEN)


def external_api_error_response(message: str = ResponseMessage.EXTERNAL_API_ERROR) -> ErrorResponse:
    """构建外部API错误响应"""
    return error_response(message=message, code=ResponseCode.EXTERNAL_API_ERROR)


def service_unavailable_response(message: str = ResponseMessage.SERVICE_UNAVAILABLE) -> ErrorResponse:
    """构建服务不可用响应"""
    return error_response(message=message, code=ResponseCode.SERVICE_UNAVAILABLE)

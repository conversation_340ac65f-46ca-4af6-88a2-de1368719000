"""
自定义异常类
"""


class BaseAPIException(Exception):
    """API异常基类"""
    
    def __init__(self, message: str, status_code: int = 500):
        self.message = message
        self.status_code = status_code
        super().__init__(self.message)


class FetcherError(BaseAPIException):
    """数据获取异常"""
    
    def __init__(self, message: str):
        super().__init__(message, status_code=502)


class ValidationError(BaseAPIException):
    """数据验证异常"""
    
    def __init__(self, message: str):
        super().__init__(message, status_code=400)


class NotFoundError(BaseAPIException):
    """资源未找到异常"""
    
    def __init__(self, message: str):
        super().__init__(message, status_code=404)


class ServiceUnavailableError(BaseAPIException):
    """服务不可用异常"""
    
    def __init__(self, message: str):
        super().__init__(message, status_code=503)


# --- 认证和授权异常 ---

class InactiveUserException(BaseAPIException):
    """用户未激活或被禁用异常"""
    def __init__(self, message: str = "用户已被禁用"):
        super().__init__(message, status_code=403)


class UserAlreadyExistsException(BaseAPIException):
    """用户已存在异常"""
    def __init__(self, message: str = "用户已存在"):
        super().__init__(message, status_code=409)


class InvalidTokenException(BaseAPIException):
    """无效Token异常"""
    def __init__(self, message: str = "无效或已过期的Token"):
        super().__init__(message, status_code=401)


class InsufficientPermissionsException(BaseAPIException):
    """权限不足异常"""
    def __init__(self, message: str = "权限不足"):
        super().__init__(message, status_code=403)

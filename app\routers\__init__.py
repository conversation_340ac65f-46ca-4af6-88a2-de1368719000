# API路由模块
from fastapi import APIRouter, FastAPI
from app.routers import criteria, user, auth, bid, company, agent, statistics, enum
from app.core.security import auth_current_user
from fastapi import Depends
from app.core.config import settings
from app.utils.response import success_response


def register_routers(app: FastAPI):
    router = APIRouter(prefix="/api/v1")
    router.include_router(user.router, dependencies=[Depends(auth_current_user)])
    router.include_router(auth.router)
    router.include_router(enum.router)
    router.include_router(bid.router, dependencies=[Depends(auth_current_user)])
    router.include_router(company.router, dependencies=[Depends(auth_current_user)])
    router.include_router(agent.router, dependencies=[Depends(auth_current_user)])
    router.include_router(statistics.router, dependencies=[Depends(auth_current_user)])
    router.include_router(criteria.criteria_router, dependencies=[Depends(auth_current_user)])
    router.include_router(criteria.bid_router, dependencies=[Depends(auth_current_user)])
    app.include_router(router)

    @app.get("/", summary="服务状态检查")
    async def root():
        """
        服务根路径，返回服务基本信息。
        """
        data = {
            "service": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "status": "running",
            "docs_url": "/docs",
            "redoc_url": "/redoc"
        }
        return success_response(data=data, message="服务运行正常")


    @app.get("/health", summary="健康检查")
    async def health_check():
        """
        健康检查端点，用于监控服务状态。
        """
        import time
        import psutil
        import os

        # 获取系统信息
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()

        data = {
            "status": "healthy",
            "service": settings.APP_NAME,
            "version": settings.APP_VERSION,
            "timestamp": int(time.time()),
            "uptime": time.time() - process.create_time(),
            "memory": {
                "rss": memory_info.rss,
                "vms": memory_info.vms,
                "percent": process.memory_percent()
            },
            "cpu_percent": process.cpu_percent()
        }

        return success_response(data=data, message="服务健康状态正常")

__all__ = ["register_routers"]
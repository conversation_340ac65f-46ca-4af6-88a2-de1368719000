"""
核心安全功能: 密码哈希、JWT令牌生成与验证
"""
from datetime import datetime, timedelta
from typing import Any, Union, Optional, List, Set, Annotated

from fastapi import Depends, HTTPException, status, Header, Request
from jose import jwt, JWTError
from passlib.context import CryptContext
from redis.asyncio import Redis

from app.core.config import settings
from app.core.exceptions import InvalidTokenException
from app.schemas.token import TokenPayload
from app.models.user import User
from app.crud import crud_user
from app.core.database import get_db
from sqlalchemy.orm import Session
import uuid


# 密码哈希上下文
# 使用 bcrypt 算法，并标记未来的哈希将使用此算法
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    验证明文密码与哈希密码是否匹配
    :param plain_password: 明文密码
    :param hashed_password: 哈希后的密码
    :return: 如果匹配则返回 True, 否则返回 False
    """
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """
    生成密码的哈希值
    :param password: 明文密码
    :return: 哈希后的密码字符串
    """
    return pwd_context.hash(password)


def create_access_token(user: User) -> str:
    """
    创建 JWT access token
    :param subject: token 的主题, 通常是用户ID或其他唯一标识符
    :param expires_delta: token 的可选过期时间
    :return: JWT 字符串
    """
    iat = datetime.now()
    expire = iat + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    if expire > user.expires_at: # type: ignore[operator]
        expire = user.expires_at
    
    to_encode = {"exp": expire, "sub": user.id, "iat": iat, "jti": str(uuid.uuid4())}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def create_refresh_token(user: User) -> str:
    """
    创建 JWT refresh token
    :param subject: token 的主题, 通常是用户ID或其他唯一标识符
    :param expires_delta: token 的可选过期时间
    :return: JWT 字符串
    """
    iat = datetime.now()
    expire = iat + timedelta(days=settings.REFRESH_TOKEN_EXPIRE_DAYS)
    if expire > user.expires_at: # type: ignore[operator]
        expire = user.expires_at

    to_encode = {"exp": expire, "sub": user.id, "iat": iat, "jti": str(uuid.uuid4())}
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt


def decode_token(token: str) -> TokenPayload:
    """
    解码 JWT token
    :param token: JWT 字符串
    :return: TokenPayload 数据模型
    :raises JWTError: 如果 token 无效或已过期
    """
    payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
    return TokenPayload(**payload)


def auth_current_user(
    token: Annotated[str, Header(alias=settings.HEADER_TOKEN_NAME)],
    request: Request
) -> str:
    """
    认证当前用户
    :param token: JWT 字符串
    :return: 认证结果
    """
    try:
        token_data = decode_token(token)
    except JWTError as e:
        raise InvalidTokenException("无效或已过期的Token")
    uid = token_data.sub
    request.state.user_id = uid
    return uid


def get_current_user_id(uid: str = Depends(auth_current_user)) -> str:
    """
    获取当前用户ID
    :param request: Request 对象
    :return: 当前用户ID
    """
    return uid


def get_current_user(
    uid: Annotated[str, Depends(get_current_user_id)], 
    db: Annotated[Session, Depends(get_db)],
    request: Request
) -> User:
    """
    获取当前用户
    :param token: JWT 字符串
    :return: User 数据模型
    """
    user = crud_user.get_user_with_permissions(db, user_id=uid)
    if not user:
        raise InvalidTokenException("无效或已过期的Token")
    request.state.user = user
    return user


class PermissionChecker:
    """
    权限检查器依赖工厂。
    
    这是一个类，你可以用需要的权限列表来实例化它，
    然后 FastAPI 会将这个实例作为依赖项来调用。
    
    用法示例:
    @router.get(
        "/some-protected-route",
        dependencies=[Depends(PermissionChecker(["user:read", "user:edit"]))]
    )
    """
    def __init__(self, required_permissions: List[str]):
        self.required_permissions = set(required_permissions)

    def __call__(self, current_user: Annotated[User, Depends(get_current_user)]):
        # 从用户的所有角色中，提取出一个包含所有不重复权限名称的集合
        user_permissions = {
            permission.name
            for role in current_user.roles
            for permission in role.permissions
        }
        
        # 检查用户拥有的权限集合，是否是所需权限集合的“超集”
        if not self.required_permissions.issubset(user_permissions):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="权限不足，无法执行此操作。",
            )
"""
HiAgent客户端工具类
"""
import httpx
import json
import logging
from typing import Iterator, Optional, Dict, Any, AsyncIterator

# 使用logging模块，而不是print
logger = logging.getLogger(__name__)


class SSEEvent:
    """
    A representation of a Server-Sent Event.
    """
    def __init__(self, data: str = '', event: str = 'message', id: Optional[str] = None, retry: Optional[int] = None):
        self.data = data
        self.event = event
        self.id = id
        self.retry = retry

    def __repr__(self) -> str:
        return f"SSEEvent(id={self.id}, event={self.event}, data='{self.data}')"


class HiAgentClient:
    """HiAgent API客户端"""
    
    def __init__(self, api_key: str, uid: str = "567", host: str = "https://ai.zhonglian.com/api/proxy/api/v1"):
        self.api_key = api_key
        self.uid = uid
        self.host = host
        self._client = httpx.AsyncClient(base_url=self.host, timeout=300.0)

    async def _send_request(self, method: str, endpoint: str, data: Optional[Dict[str, Any]] = None) -> httpx.Response:
        headers = {"Apikey": self.api_key}
        if data is None:
            data = {}
        data.update({"UserID": self.uid})
        
        response = await self._client.request(method, endpoint, headers=headers, json=data)
        
        req = response.request
        body_str = req.content.decode('utf-8') if req.content else "{}"
        # 使用logger.debug记录日志
        logger.debug(
            f"Request: {req.method} {req.url}\nHeaders: {dict(req.headers)}\nData: {body_str}\nStatus Code: {response.status_code}"
        )
        response.raise_for_status()
        return response

    async def _parse_sse(self, response: httpx.Response) -> AsyncIterator[SSEEvent]:
        """异步解析SSE事件"""
        current_event = SSEEvent()
        async for line in response.aiter_lines():
            if not line:
                if current_event.data:
                    yield current_event
                    current_event = SSEEvent()
                continue

            if line.startswith(':'):
                continue

            if ':' not in line:
                continue
            
            field, value = line.split(':', 1)
            value = value.strip()

            if field == 'data':
                current_event.data = f"{current_event.data}\n{value}" if current_event.data else value
            elif field == 'event':
                current_event.event = value
            elif field == 'id':
                current_event.id = value
            elif field == 'retry':
                try:
                    current_event.retry = int(value)
                except (ValueError, TypeError):
                    pass
        
        if current_event.data:
            # Yield the last event if the stream ends without a blank line
            yield current_event

    async def create_conversation(self) -> httpx.Response:
        data = {
            "Inputs": {},
        }
        return await self._send_request("POST", "create_conversation", data)

    async def send_query(self, query: str, conversation_id: str) -> httpx.Response:
        data = {
            "Query": query,
            "AppConversationID": conversation_id,
            "ResponseMode": "blocking",
            "QueryExtends": {
                "Files": [],
            },
            "PubAgentJump": True,
        }
        return await self._send_request("POST", "chat_query_v2", data)

    async def send_query_stream(self, query: str, conversation_id: str) -> AsyncIterator[SSEEvent]:
        """以异步流模式发送查询"""
        headers = {"Apikey": self.api_key}
        data = {
            "Query": query,
            "AppConversationID": conversation_id,
            "ResponseMode": "streaming",
            "QueryExtends": {
                "Files": [],
            },
            "PubAgentJump": True,
        }
        data.update({"UserID": self.uid})
        url = "chat_query_v2"
        
        async with self._client.stream("POST", url, headers=headers, json=data) as response:
            req = response.request
            body_str = req.content.decode('utf-8') if req.content else "{}"
            logger.debug(
                f"Request: {req.method} {req.url}\nHeaders: {dict(req.headers)}\nData: {body_str}\nStatus Code: {response.status_code}"
            )
            response.raise_for_status()
            async for event in self._parse_sse(response):
                yield event

    async def query_again(self, conversation_id: str, message_id: str) -> httpx.Response:
        """重新生成回复"""
        data = {
            "AppConversationID": conversation_id,
            "MessageID": message_id,
        }
        return await self._send_request("POST", "query_again", data)

    async def get_app_config_preview(self) -> httpx.Response:
        """获取应用配置"""
        return await self._send_request("POST", "get_app_config_preview")

    async def get_conversation_list(self) -> httpx.Response:
        """获取对话列表"""
        return await self._send_request("POST", "get_conversation_list")

    async def get_conversation_inputs(self, conversation_id: str) -> httpx.Response:
        """获取对话变量输入"""
        data = {
            "AppConversationID": conversation_id,
        }
        return await self._send_request("POST", "get_conversation_inputs", data)

    async def update_conversation(self, conversation_id: str, name: Optional[str] = None, inputs: Optional[dict] = None) -> httpx.Response:
        """更新会话"""
        data = {}
        data.update({
            "AppConversationID": conversation_id,
        })
        if name:
            data.update({"ConversationName": name})
        if inputs:
            data.update({"Inputs": inputs})
        return await self._send_request("POST", "update_conversation", data)

    async def delete_conversation(self, conversation_id: str) -> httpx.Response:
        """删除会话"""
        data = {
            "AppConversationID": conversation_id,
        }
        return await self._send_request("POST", "delete_conversation", data)

    async def stop_message(self, conversation_id: str, message_id: str) -> httpx.Response:
        """停止响应"""
        data = {
            "AppConversationID": conversation_id,
            "MessageID": message_id,
        }
        return await self._send_request("POST", "stop_message", data)

    async def get_conversation_messages(self, conversation_id: str, limit: int = 10) -> httpx.Response:
        """获取会话历史消息列表"""
        data = {
            "AppConversationID": conversation_id,
            "Limit": limit,
        }
        return await self._send_request("POST", "get_conversation_messages", data)

    async def get_message_info(self, conversation_id: str, message_id: str) -> httpx.Response:
        """获取消息详情"""
        data = {
            "AppConversationID": conversation_id,
            "MessageID": message_id,
        }
        return await self._send_request("POST", "get_message_info", data)

    async def delete_message(self, message_id: str) -> httpx.Response:
        """删除消息"""
        data = {
            "MessageID": message_id,
        }
        return await self._send_request("POST", "delete_message", data)

    async def feedback_message(self, message_id: str, feedback: int) -> httpx.Response:
        """回答反馈评价（赞或踩）"""
        data = {
            "MessageID": message_id,
            "LikeType": feedback,
        }
        return await self._send_request("POST", "feedback", data)

    async def set_message_answer_used(self, conversation_id: str, message_id: str, answer_used: int) -> httpx.Response:
        """多组回答时，设置某个回答为默认使用的回答"""
        data = {
            "AppConversationID": conversation_id,
            "MessageID": message_id,
            "AnswerUsed": answer_used,
        }
        return await self._send_request("POST", "set_message_answer_used", data)

    async def get_suggested_questions(self, message_id: str) -> httpx.Response:
        """获取提问建议"""
        data = {
            "MessageID": message_id,
        }
        return await self._send_request("POST", "get_suggested_questions", data)

    async def run_app_workflow(self, inputs: dict, no_debug: bool = True) -> httpx.Response:
        """测试运行工作流(异步)"""
        data = {
            "InputData": json.dumps(inputs),
            "NoDebug": no_debug,
        }
        return await self._send_request("POST", "run_app_workflow", data)

    async def query_run_app_process(self, run_id: str) -> httpx.Response:
        """查询测试运行工作流运行进展(异步)"""
        data = {
            "RunID": run_id,
        }
        return await self._send_request("POST", "query_run_app_process", data)

    async def list_oauth2_token(self) -> httpx.Response:
        """查询用户oauth2登录信息的接口"""
        return await self._send_request("POST", "list_oauth2_token")

    async def aclose(self):
        """Closes the underlying httpx client."""
        await self._client.aclose()

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.aclose()
"""
FastAPI 标讯数据接口主应用
"""
from contextlib import asynccontextmanager
from sqlalchemy.orm import Session
from fastapi import FastAPI
from datetime import datetime, timedelta
from app.core.config import settings
from app.core.logging import setup_logging, get_logger
from app.core.redis import close_redis_pool, get_redis_pool
from app.core.database import engine, Base, get_db
from app.core.scheduler import start_scheduler, stop_scheduler
from app.core.mail_client import mail_client
from app.models import company as company_model, criteria as subscribe_model, user as user_model # 导入模型模块，以便 Base 能够发现它们
from app.core.middleware import register_middleware
from app.routers import register_routers
from app.core.exceptions import register_exception_handlers

# 设置日志
setup_logging()
logger = get_logger(__name__)


def create_init_data(db: Session):
    """初始化数据库数据"""
    user = user_model.User(
        id="123",
        sso_id="123",
        username="admin",
        email="<EMAIL>",
        expires_at=datetime.now() + timedelta(days=30),
    )
    role = user_model.Role(
        id="123",
        name="admin",
        description="管理员",
    )
    permission = user_model.Permission(
        id="123",
        name="admin",
        description="管理员",
    )
    user.roles.append(role)
    role.permissions.append(permission)
    db.add(user)
    db.add(role)
    db.add(permission)
    db.commit()
    db.refresh(user)
    db.refresh(role)
    db.refresh(permission)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logger.info("启动 FastAPI 标讯数据接口服务...")
    logger.info(f"服务版本: {settings.APP_VERSION}")
    logger.info(f"调试模式: {settings.DEBUG}")
    await get_redis_pool()
    logger.info("Redis 连接池已创建。")
    db = next(get_db())

    logger.info("")
    await mail_client.connect()
    
    # 创建数据库表
    try:
        if settings.DEBUG:
            Base.metadata.drop_all(bind=engine)
            logger.info("数据库表已删除。")
            Base.metadata.create_all(bind=engine)
            create_init_data(db)
        logger.info("数据库表已成功创建或验证。")
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")

    logger.info("启动 apscheduler 调度器...")
    await start_scheduler(app)
    logger.info("apscheduler 调度器已启动。")

    yield

    await mail_client.close()
    logger.info("")

    # 关闭时的清理
    await close_redis_pool()
    logger.info("Redis 连接池已关闭。")
    await stop_scheduler()
    logger.info("apscheduler 调度器已停止。")
    logger.info("关闭 FastAPI 标讯数据接口服务...")


# 创建 FastAPI 应用实例
app = FastAPI(
    title=settings.APP_NAME,
    description=settings.APP_DESCRIPTION,
    version=settings.APP_VERSION,
    debug=settings.DEBUG,
    lifespan=lifespan
)

# 注册路由、异常处理器、中间件
register_routers(app)
register_exception_handlers(app)
register_middleware(app)
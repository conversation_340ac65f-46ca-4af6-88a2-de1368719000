"""
数据库连接和会话管理模块
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.core.config import settings

# 创建数据库引擎
engine = create_engine(
    settings.DATABASE_URL, 
    pool_pre_ping=True,
    pool_recycle=3600
)

# 创建数据库会话类
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建数据模型基类
Base = declarative_base()

def get_db():
    """
    获取数据库会话的依赖项函数
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
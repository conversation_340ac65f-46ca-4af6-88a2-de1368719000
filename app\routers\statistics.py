"""
统计分析相关API路由
"""
from fastapi import APIRouter, Path, Query, Depends

from app.schemas.statistics import (
    CompanyPartnersResponse,
    CompanyBidStatistics,
    IndustryStatistics
)
from app.services.statistics_service import StatisticsService, get_statistics_service
from app.utils.response import success_response
from app.schemas.response import ApiResponse

router = APIRouter(prefix="/statistics", tags=["统计分析"])


@router.get("/company/{company_name}/partners", summary="获取公司合作伙伴", response_model=ApiResponse[CompanyPartnersResponse])
async def get_company_partners(
    company_name: str = Path(..., description="公司名称"),
    service: StatisticsService = Depends(get_statistics_service)
):
    """
    获取指定公司的合作伙伴信息。

    该接口会分析公司作为采购单位和中标单位的所有项目，
    统计与其合作的其他公司及合作次数。

    参数:
    - company_name: 公司名称

    返回:
    - 合作伙伴信息，按合作次数降序排列
    """
    partners = await service.get_company_partners(company_name)
    data = CompanyPartnersResponse(
        company_name=company_name,
        partners=partners
    )
    return success_response(data=data, message="获取公司合作伙伴成功")


@router.get("/company/{company_name}/bid-stats", summary="获取公司投标统计", response_model=ApiResponse[CompanyBidStatistics])
async def get_company_bid_statistics(
    company_name: str = Path(..., description="公司名称"),
    service: StatisticsService = Depends(get_statistics_service)
):
    """
    获取公司的投标统计信息。

    包括投标项目数量、中标项目数量和中标率等信息。

    参数:
    - company_name: 公司名称

    返回:
    - 公司投标统计信息
    """
    data = await service.get_company_bid_statistics(company_name)
    return success_response(data=data, message="获取公司投标统计成功")


@router.get("/industry/{industry}", summary="获取行业统计信息", response_model=ApiResponse[IndustryStatistics])
async def get_industry_statistics(
    industry: str = Path(..., description="行业分类"),
    limit: int = Query(100, description="分析的项目数量限制"),
    service: StatisticsService = Depends(get_statistics_service)
):
    """
    获取指定行业的统计信息。

    包括行业项目总数、主要参与公司、平均预算等信息。

    参数:
    - industry: 行业分类
    - limit: 分析的项目数量限制，默认100

    返回:
    - 行业统计信息
    """
    data = await service.get_industry_statistics(industry, limit)
    return success_response(data=data, message="获取行业统计信息成功")

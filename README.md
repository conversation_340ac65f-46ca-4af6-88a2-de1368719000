# 标讯数据接口服务

一个基于 FastAPI 的高可扩展性标讯数据接口服务，提供标讯查询、公司信息、智能对话和统计分析等功能。

## 功能特性

- 🚀 **高性能**: 基于 FastAPI 和异步编程，提供高性能的 API 服务
- 📊 **标讯数据**: 支持标讯列表查询、详情获取、公司投标/中标项目搜索
- 🏢 **公司信息**: 提供公司基本信息、专利列表和专利详情查询
- 🤖 **智能对话**: 集成 HiAgent 智能对话功能，支持概率预测和关键因素分析
- 📈 **统计分析**: 提供公司合作伙伴分析、投标统计和行业统计功能
- 🔧 **模块化设计**: 采用清晰的分层架构，易于维护和扩展
- 📝 **完整文档**: 自动生成的 API 文档和详细的使用说明

## 项目结构

```
├── app/                    # 主应用目录
│   ├── __init__.py
│   ├── main.py            # FastAPI 应用入口
│   ├── core/              # 核心配置
│   │   ├── __init__.py
│   │   ├── config.py      # 应用配置
│   │   └── exceptions.py  # 自定义异常
│   ├── models/            # Pydantic 数据模型
│   │   ├── __init__.py
│   │   ├── agent.py       # 智能对话模型
│   │   ├── bid.py         # 标讯数据模型
│   │   ├── company.py     # 公司数据模型
│   │   └── statistics.py  # 统计分析模型
│   ├── services/          # 业务逻辑服务
│   │   ├── __init__.py
│   │   ├── agent_service.py      # 智能对话服务
│   │   ├── bid_service.py        # 标讯数据服务
│   │   ├── company_service.py    # 公司数据服务
│   │   └── statistics_service.py # 统计分析服务
│   ├── routers/           # API 路由
│   │   ├── __init__.py
│   │   ├── agent.py       # 智能对话路由
│   │   ├── bid.py         # 标讯数据路由
│   │   ├── company.py     # 公司数据路由
│   │   └── statistics.py  # 统计分析路由
│   └── utils/             # 工具模块
│       ├── __init__.py
│       └── hiagent_client.py # HiAgent 客户端
├── tests/                 # 测试目录
│   ├── __init__.py
│   ├── conftest.py       # pytest 配置
│   └── test_main.py      # 主应用测试
├── .env.example          # 环境变量示例
├── requirements.txt      # Python 依赖
├── pyproject.toml       # 项目配置
├── run.py               # 启动脚本
└── README.md            # 项目文档
```

## 快速开始

### 1. 环境准备

确保您的系统已安装 Python 3.8 或更高版本。

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

### 3. 配置环境变量

复制环境变量示例文件并配置相应参数：

```bash
cp .env.example .env
```

编辑 `.env` 文件，配置以下关键参数：

```env
# 建安大数据API配置
BID_API_APPID=your_bid_api_appid
BID_API_KEY=your_bid_api_key

# 厦门市信用综合服务平台API配置
COMPANY_API_KEY_897=your_company_api_key_897
COMPANY_API_KEY_945=your_company_api_key_945
COMPANY_API_KEY_943=your_company_api_key_943

# HiAgent配置
HIAGENT_QUERY_KEY=your_hiagent_query_key
HIAGENT_PROBABILITY_KEY=your_hiagent_probability_key
HIAGENT_KEY_FACTOR_KEY=your_hiagent_key_factor_key
```

### 4. 启动服务

使用启动脚本：

```bash
python run.py
```

或者直接使用 uvicorn：

```bash
uvicorn app.main:app --reload
```

### 5. 访问服务

- **API 文档**: http://127.0.0.1:8000/docs
- **ReDoc 文档**: http://127.0.0.1:8000/redoc
- **健康检查**: http://127.0.0.1:8000/health

## API 接口

### 标讯数据 (`/bids`)

- `POST /bids/query` - 查询标讯列表
- `GET /bids/{bid_id}` - 获取标讯详情
- `POST /bids/company/tenders` - 搜索公司投标项目
- `POST /bids/company/wins` - 搜索公司中标项目

### 公司数据 (`/company`)

- `GET /company/info/{keyword}` - 获取公司基本信息
- `GET /company/patents/{keyword}` - 获取公司专利列表
- `GET /company/patent/detail/{application_number}` - 获取专利详情

### 智能对话 (`/agent`)

- `POST /agent/conversations` - 创建对话
- `POST /agent/conversations/{conversation_id}/query` - 发送查询
- `GET /agent/conversations/{conversation_id}/messages` - 获取对话消息
- `POST /agent/predict/probabilities` - 预测中标概率
- `POST /agent/project/keyfactors` - 获取项目关键因素

### 统计分析 (`/statistics`)

- `GET /statistics/company/{company_name}/partners` - 获取公司合作伙伴
- `GET /statistics/company/{company_name}/bid-stats` - 获取公司投标统计
- `GET /statistics/industry/{industry}` - 获取行业统计信息

## 开发指南

### 运行测试

```bash
pytest
```

### 代码格式化

```bash
black app/ tests/
isort app/ tests/
```

### 类型检查

```bash
mypy app/
```

### 开发模式启动

```bash
python run.py --reload --log-level debug
```

## 部署

### 生产环境启动

```bash
python run.py --host 0.0.0.0 --port 8000 --workers 4
```

### Docker 部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "run.py", "--host", "0.0.0.0", "--port", "8000"]
```

## 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `APP_NAME` | 应用名称 | 标讯数据接口 |
| `DEBUG` | 调试模式 | false |
| `HOST` | 服务器主机 | 127.0.0.1 |
| `PORT` | 服务器端口 | 8000 |
| `LOG_LEVEL` | 日志级别 | INFO |

### API 密钥配置

请联系相应的服务提供商获取 API 密钥：

- **建安大数据**: 标讯数据查询服务
- **厦门市信用综合服务平台**: 公司和专利信息服务
- **HiAgent**: 智能对话和分析服务

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 更新日志

### v2.0.0
- 重构为模块化架构
- 添加统计分析功能
- 完善错误处理和日志记录
- 添加完整的测试覆盖
- 改进 API 文档和类型提示

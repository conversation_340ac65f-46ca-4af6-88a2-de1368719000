from fastapi import APIRouter, Depends, Query, Body, Response, <PERSON><PERSON>
from typing import Annotated
from app.schemas.token import Token
from app.schemas.response import ApiResponse
from app.services.auth_service import AuthService, get_auth_service
from app.utils.response import success_response
from app.core.database import get_db
from sqlalchemy.orm import Session
from app.core.config import settings


router = APIRouter(prefix="/auth", tags=["认证"])


@router.post("/sso-login", summary="SSO登录", response_model=ApiResponse[Token])
async def sso_login(
    sso_token: Annotated[str, Query(..., description="SSO Token")], 
    db: Annotated[Session, Depends(get_db)], 
    service: Annotated[AuthService, Depends(get_auth_service)],
    response: Response
):
    token_data = service.sso_login(db, sso_token)
    response.set_cookie(
        key="refresh_token",
        value=token_data.refresh_token,
        httponly=True,
        max_age=60 * 60 * 24 * settings.REFRESH_TOKEN_EXPIRE_DAYS,
    )
    return success_response(data=token_data)


@router.post("/refresh-token", summary="刷新Token", response_model=ApiResponse[Token])
async def refresh_token(
    refresh_token: Annotated[str, Cookie(..., description="Refresh Token")], 
    db: Annotated[Session, Depends(get_db)], 
    service: Annotated[AuthService, Depends(get_auth_service)],
    response: Response
):
    token_data = service.refresh_token(db, refresh_token)
    response.set_cookie(
        key="refresh_token",
        value=token_data.refresh_token,
        httponly=True,
        max_age=60 * 60 * 24 * settings.REFRESH_TOKEN_EXPIRE_DAYS,
    )
    return success_response(data=token_data)
import asyncio, json
from apscheduler.jobstores.sqlalchemy import SQLAlchemyJobStore
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from fastapi import FastAPI
from apscheduler.events import EVENT_JOB_EXECUTED, EVENT_JOB_ADDED, EVENT_JOB_REMOVED

from app.core.config import settings

_scheduler: AsyncIOScheduler

async def start_scheduler(app: FastAPI):
    """在服务启动时, 先把scheduler起起来, 再用用户配置热填充任务"""
    global _scheduler
    jobstores = {
        'default': SQLAlchemyJobStore(url=settings.DATABASE_URL)
    }
    _scheduler = AsyncIOScheduler(
        jobstores=jobstores,
        timezone="Asia/Shanghai"
    )
    
    # 假设任务配置存储在 redis 或 DB；这里为了演示 hard-code 几条
    # for cfg in _example_user_configs():
    #     _scheduler.add_job(
    #         func=_run_task,                 # 实际要做的事
    #         trigger=_build_trigger(cfg),    # 根据用户输入动态创建trigger
    #         id=cfg["id"],
    #         args=[cfg["payload"]],
    #         replace_existing=True,
    #         max_instances=1                 # 防止同一任务并发爆内存
    #     )
    _scheduler.start()

    app.state.scheduler = _scheduler        # 把对象挂到 app.state，后续接口可直接操作


async def stop_scheduler():
    """优雅下线"""
    _scheduler.shutdown(wait=True)

# ---------------------------- 以下为示例辅助函数 ---------------------------- #

def _example_user_configs():
    """这里可以换成从 mysql/redis 查"""
    return [
        {
            "id": "backup",
            "type": "interval",
            "minutes": 5,
            "payload": {"cmd": "aws s3 sync …"}
        },
        {
            "id": "send-reports",
            "type": "cron",
            "expr": "0 18 * * 1-5",        # 工作日 18:00
            "payload": {"email": "<EMAIL>"}
        },
    ]

def _build_trigger(cfg: dict):
    kind = cfg["type"]
    if kind == "interval":
        return IntervalTrigger(**{k: v for k, v in cfg.items() if k in {"seconds", "minutes", "hours"}})
    if kind == "cron":
        return CronTrigger.from_crontab(cfg["expr"])
    raise ValueError(f"unknown type: {kind}")

async def _run_task(payload: dict):
    """真正执行的异步函数，可以是任何长耗时 IO(数据库、HTTP)"""
    await asyncio.sleep(2)
    print("TASK =>", payload)

def get_scheduler() -> AsyncIOScheduler:
    """获取全局调度器实例"""
    if not _scheduler:
        raise RuntimeError("Scheduler not started. Call start_scheduler first.")
    return _scheduler

from app.schemas.user import SSOUser, User, UserUpdate
from app.models.user import User as UserModel
from sqlalchemy.orm import Session
from app.crud import crud_user
import uuid
from datetime import datetime

class UserService:
    def __init__(self):
        pass

    def register_user(self, db: Session, user: SSOUser) -> User:
        """
        将SSO用户注册到数据库中
        """
        # 检查用户是否存在
        if crud_user.get_user_by_sso_id(db, user.id):
            raise ValueError("用户已存在")
        
        # 创建用户
        user = UserModel(id=uuid.uuid4(), sso_id=user.id)
        db.add(user)
        db.commit()
        db.refresh(user)
        return User.model_validate(user)

    def activate_user(self, db: Session, user: UserModel, expires_at: datetime) -> User:
        """
        激活用户
        """
        user.expires_at = expires_at # type: ignore
        db.commit()
        db.refresh(user)
        return user

    def update_user(self, db: Session, user: UserModel, update_user: UserUpdate) -> User:
        """
        更新用户信息
        """
        user.email = update_user.email # type: ignore
        user.phone = update_user.phone # type: ignore
        user.username = update_user.username # type: ignore
        db.commit()
        db.refresh(user)
        return User.model_validate(user)


user_service = UserService()

def get_user_service() -> UserService:
    return user_service
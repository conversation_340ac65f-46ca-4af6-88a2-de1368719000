from sqlalchemy import Column, Enum, Integer, String, DateTime, func, ForeignKey, Boolean
from sqlalchemy.orm import relationship

from app.core.database import Base
from app.core.enum import PublishTimeScopeEnum, BudgetScopeEnum


class SearchCriteria(Base):
    """搜索条件"""
    __tablename__ = "search_criteria"

    id = Column(String(36), primary_key=True, index=True)
    name = Column(String(255), comment="搜索条件名称")
    keywords = Column(String(255), comment="搜索条件查询语句")
    keyword_scope = Column(String(255), comment="关键词范围")
    first_tier_industry = Column(String(255), comment="一级行业分类")
    second_tier_industry = Column(String(255), comment="二级行业分类")
    region = Column(String(255), comment="地区")
    budget_start = Column(Integer, comment="预算开始")
    budget_end = Column(Integer, comment="预算结束")
    budget_scope = Column(Enum(BudgetScopeEnum, native_enum=False), comment="预算范围")
    publish_time_start = Column(DateTime, comment="发布时间开始")
    publish_time_end = Column(DateTime, comment="发布时间结束")
    publish_time_scope = Column(Enum(PublishTimeScopeEnum, native_enum=False), comment="发布时间范围")
    top_type = Column(String(255), comment="一级公告类型")
    sub_type = Column(String(255), comment="二级公告类型")

    is_tracked = Column(Boolean, default=False, index=True, comment="是否已订阅")

    user_id = Column(String(36), ForeignKey('users.id'))
    user = relationship("User", back_populates="search_criteria")

    selected_at = Column(DateTime, server_default=func.now(), comment="上次选中时间")
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
"""
应用配置管理模块
"""
from typing import Optional
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本配置
    APP_NAME: str = "标讯数据接口"
    APP_VERSION: str = "2.0.0"
    APP_DESCRIPTION: str = "一个基于 FastAPI 的高可扩展性标讯数据接口服务"
    DEBUG: bool = True
    
    # 服务器配置
    HOST: str = "127.0.0.1"
    PORT: int = 8000
    
    # 建安大数据API配置
    BID_API_APPID: str = "sfNRBYRAEFBAABBQYBCSkf"
    BID_API_KEY: str = "482AlQr1"
    BID_API_URL_LIST: str = "https://api.jianyu360.com/data/biddata/list"
    BID_API_URL_INFO: str = "https://api.jianyu360.com/data/biddata/info"
    
    # 厦门市信用综合服务平台API配置
    COMPANY_API_BASE_URL: str = "https://api.xmcic.cn:8443/government/economic"
    COMPANY_API_KEY_897: str = "f90393700d1b215bd861da174b7a396d"  # 公司基本信息
    COMPANY_API_KEY_945: str = "ad2022ced062df34ca3302617ffafb26"  # 专利列表
    COMPANY_API_KEY_943: str = "51df265a42c636f3df28004fccc8dca1"  # 专利详情
    
    # HiAgent配置
    HIAGENT_ENDPOINT: str = "https://ai.zhonglian.com/api/proxy/api/v1"
    HIAGENT_INQUIRY_KEY: str = "d199pbnr8lobce3ri730"
    HIAGENT_PROBABILITY_KEY: str = "d19uginr8lobce3ric0g"
    HIAGENT_KEY_FACTOR_KEY: str = "d1a5rdfr8lobce3ricj0"
    HIAGENT_UID: str = "567"

    # JWT 认证配置
    # 建议使用 `openssl rand -hex 32` 生成一个安全的密钥，并存储在 .env 文件中
    SECRET_KEY: str = "your-secret-key-here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30  # 访问令牌有效期（分钟）
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7     # 刷新令牌有效期（天）
    HEADER_TOKEN_NAME: str = "Authorization"

    # Redis缓存配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # MySQL数据库配置
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_USER: str = "root"
    DB_PASSWORD: str = "root"
    DB_NAME: str = "bid_data"

    SENDER_EMAIL: str
    SENDER_PASSWORD: str
    SENDER_SMTP_SERVER: str
    SENDER_SMTP_PORT: int
    
    @property
    def DATABASE_URL(self) -> str:
        """构造数据库连接URL"""
        return f"mysql+pymysql://{self.DB_USER}:{self.DB_PASSWORD}@{self.DB_HOST}:{self.DB_PORT}/{self.DB_NAME}?charset=utf8mb4"

    # CORS配置
    CORS_ORIGINS: list[str] = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: list[str] = ["*"]
    CORS_ALLOW_HEADERS: list[str] = ["*"]
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


# 全局配置实例
settings = Settings()

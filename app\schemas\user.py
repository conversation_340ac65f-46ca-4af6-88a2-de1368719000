"""
用户、角色、权限相关的 Pydantic 模型
"""
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field
from datetime import datetime


# --- Permission Schemas ---
class PermissionBase(BaseModel):
    name: str = Field(..., max_length=50, description="权限名称 (e.g., user:create)")
    description: Optional[str] = Field(None, max_length=255, description="权限描述")


class PermissionCreate(PermissionBase):
    pass


class PermissionUpdate(PermissionBase):
    pass


class Permission(PermissionBase):
    id: str
    created_at: datetime

    class Config:
        from_attributes = True


# --- Role Schemas ---
class RoleBase(BaseModel):
    name: str = Field(..., max_length=50, description="角色名称")
    description: Optional[str] = Field(None, max_length=255, description="角色描述")


class RoleCreate(RoleBase):
    pass


class RoleUpdate(RoleBase):
    pass


class Role(RoleBase):
    id: str
    created_at: datetime
    permissions: List[Permission] = []

    class Config:
        from_attributes = True


# --- User Schemas ---
class UserBase(BaseModel):
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名")
    email: Optional[EmailStr] = Field(None, description="电子邮箱")
    phone: Optional[str] = Field(None, description="手机号")


class UserCreate(UserBase):
    company_name: str = Field(..., min_length=3, max_length=50, description="公司名称全称")
    user_name: str = Field(..., min_length=3, max_length=50, description="用户名称")


class UserUpdate(BaseModel):
    email: Optional[EmailStr] = Field(None, description="电子邮箱")
    phone: Optional[str] = Field(None, description="手机号")
    company_name: Optional[str] = Field(None, min_length=3, max_length=50, description="公司名称全称")
    username: Optional[str] = Field(None, min_length=3, max_length=50, description="用户名称")


class UserInDBBase(UserBase):
    id: str
    username: str
    # created_at: datetime
    # updated_at: datetime
    # roles: List[Role] = []

    class Config:
        from_attributes = True


class User(UserInDBBase):
    """用于API响应的用户模型"""
    pass


class UserInDB(UserInDBBase):
    """数据库中的用户模型（包含哈希密码）"""
    hashed_password: str


class SSOUser(BaseModel):
    id: str


class OrderInfo(BaseModel):
    order_id: str
"""
Redis 连接管理模块
"""
import redis.asyncio as redis
from redis.asyncio import Redis
from app.core.config import settings

# 全局 Redis 连接池实例
redis_pool: Redis | None = None

async def get_redis_pool() -> Redis:
    """
    获取 Redis 连接池实例。
    如果实例不存在，则会创建一个新的连接池。
    """
    global redis_pool
    if redis_pool is None:
        redis_pool = redis.from_url(
            f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}",
            db=settings.REDIS_DB,
            password=settings.REDIS_PASSWORD,
            encoding="utf-8",
            decode_responses=True  # 自动将响应从 bytes 解码为 str
        )
    return redis_pool

async def close_redis_pool():
    """
    关闭全局 Redis 连接池。
    """
    if redis_pool:
        await redis_pool.close()

async def get_redis_client() -> Redis:
    """
    依赖注入函数，用于在路由或服务中获取 Redis 客户端。
    """
    return await get_redis_pool()
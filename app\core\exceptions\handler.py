from fastapi import Request
from fastapi.responses import JSONResponse
from app.core.logging import get_logger
from app.utils.response import error_response, external_api_error_response
from app.schemas.response import ResponseCode
from app.core.config import settings
from fastapi.exceptions import RequestValidationError

from .types import BaseAPIException, FetcherError


logger = get_logger(__name__)

# 全局异常处理器
def base_api_exception_handler(request: Request, exc: BaseAPIException):
    """处理自定义API异常"""
    request_id = getattr(request.state, "request_id", "unknown")
    logger.error(
        f"API异常 [Request ID: {request_id}] {request.method} {request.url.path}: {exc.message}"
    )

    # 根据HTTP状态码映射到响应码
    code_mapping = {
        400: ResponseCode.INVALID_PARAMS,
        401: ResponseCode.UNAUTHORIZED,
        403: ResponseCode.FORBIDDEN,
        404: ResponseCode.NOT_FOUND,
        502: ResponseCode.EXTERNAL_API_ERROR,
        503: ResponseCode.SERVICE_UNAVAILABLE,
    }

    response_code = code_mapping.get(exc.status_code, ResponseCode.INTERNAL_ERROR)
    response_data = error_response(
        message=exc.message,
        code=response_code,
        data={"request_id": request_id, "type": exc.__class__.__name__}
    )

    return JSONResponse(
        status_code=exc.status_code,
        content=response_data.model_dump()
    )


def fetcher_exception_handler(request: Request, exc: FetcherError):
    """处理数据获取异常"""
    request_id = getattr(request.state, "request_id", "unknown")
    logger.error(
        f"数据获取失败 [Request ID: {request_id}] {request.method} {request.url.path}: {exc.message}"
    )

    response_data = external_api_error_response(
        message=f"数据获取失败: {exc.message}"
    )
    response_data.data = {"request_id": request_id}

    return JSONResponse(
        status_code=502,
        content=response_data.model_dump()
    )


def generic_exception_handler(request: Request, exc: Exception):
    """处理所有未捕获的异常"""
    request_id = getattr(request.state, "request_id", "unknown")
    logger.error(
        f"未处理的异常 [Request ID: {request_id}] {request.method} {request.url.path}: {str(exc)}",
        exc_info=True
    )

    if settings.DEBUG:
        # 调试模式下返回详细错误信息
        response_data = error_response(
            message="服务器内部发生未知错误",
            code=ResponseCode.INTERNAL_ERROR,
            data={
                "detail": str(exc),
                "type": exc.__class__.__name__,
                "request_id": request_id
            }
        )
    else:
        # 生产模式下返回通用错误信息
        response_data = error_response(
            message="服务器内部发生未知错误",
            code=ResponseCode.INTERNAL_ERROR,
            data={"request_id": request_id}
        )

    return JSONResponse(
        status_code=500,
        content=response_data.model_dump()
    )

def validation_exception_handler(request: Request, exc: RequestValidationError):
    """
    捕获 RequestValidationError 异常并自定义返回格式
    """
    # 从异常中获取原始的错误详情
    # exc.errors() 是一个列表，每个元素是一个包含 'loc', 'msg', 'type' 的字典
    # 例如: [{'loc': ('body', 'name'), 'msg': 'Field required', ...}]
    
    # 我们将它转换为更友好的格式
    respCode = ResponseCode.INVALID_PARAMS
    errors = {}
    for error in exc.errors():
        # error['loc'] 是一个元组，例如 ('body', 'user', 'name')
        # 我们取最后一个元素作为字段名
        field_name = ".".join(map(str, error['loc'][1:])) # 移除 'body' 或 'query' 等
        errors[field_name] = error['msg']
        if "Authorization" in field_name: # 如果是 Authorization 字段校验错误，则返回 401 状态码
            respCode = ResponseCode.UNAUTHORIZED
    return JSONResponse(
        status_code=respCode,
        content=error_response(
            message="请求参数校验失败",
            code=respCode,
            data=errors
        ).model_dump()
    )
from fastapi import APIRouter

from app.schemas.user import OrderInfo, UserCreate, UserUpdate, User
from app.services.user_service import UserService, get_user_service
from app.schemas.response import ApiResponse
from fastapi import Depends
from typing import Annotated
from app.core.security import get_current_user
from app.utils.response import success_response
from app.core.database import get_db
from sqlalchemy.orm import Session
from app.models.user import User as UserModel

router = APIRouter(prefix="/user", tags=["用户"])


@router.post("/activate", summary="注册用户")
async def activate_user(
    order: OrderInfo, 
    service: UserService = Depends(get_user_service)
):
    """
    接收门户系统订单信息，激活用户，延长用户过期时间或者创建新用户
    """
    pass


@router.post("/init", summary="初始化用户")
async def init_user(
    user: UserCreate, 
    service: UserService = Depends(get_user_service)
):
    """
    初始化用户信息，主要是用户名称和公司名称
    company_name: 公司名称全称，从接口获取公司其他信息
    """
    pass


@router.get("/info", summary="获取用户信息", response_model=ApiResponse[User])
async def get_user_info(
    user: Annotated[UserModel, Depends(get_current_user)],
    service: UserService = Depends(get_user_service),
):
    """
    获取用户信息
    """
    print("user info is: ", user)
    user_data = User.model_validate(user)
    return success_response(data=user_data)


@router.put("/info", summary="更新用户信息", response_model=ApiResponse[User])
async def update_user_info(
    update_user: UserUpdate,
    current_user: Annotated[UserModel, Depends(get_current_user)],
    db: Annotated[Session, Depends(get_db)],
    service: UserService = Depends(get_user_service),
):
    """
    更新用户信息
    """
    user = service.update_user(db, current_user, update_user)
    return success_response(data=user)
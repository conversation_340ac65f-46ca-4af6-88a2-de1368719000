"""
HiAgent智能对话服务
"""
import json
from typing import List, Dict, Any

from app.core.config import settings
from app.core.exceptions import FetcherError, ValidationError
from app.utils.hiagent_client import HiAgentClient
from app.schemas.agent import Message, QueryResponse
from app.schemas.bid import BidInfo
from app.utils.cache import redis_cache


class AgentService:
    """HiAgent智能对话服务类"""
    def __init__(self):
        self.endpoint = settings.HIAGENT_ENDPOINT
        self.inquiry_key = settings.HIAGENT_INQUIRY_KEY
        self.probability_key = settings.HIAGENT_PROBABILITY_KEY
        self.key_factor_key = settings.HIAGENT_KEY_FACTOR_KEY
        self.uid = settings.HIAGENT_UID
    
    def _get_key_by_agent_name(self, agent_name: str) -> str:
        """根据 HiAgent 的名称获取对应的 API Key"""
        agent_name_to_key = {
            "inquiry": self.inquiry_key,
            "probability": self.probability_key,
            "key_factor": self.key_factor_key,
        }
        return agent_name_to_key[agent_name]

    async def create_conversation(self, agent_name: str) -> str:
        api_key = self._get_key_by_agent_name(agent_name)
        """创建一个新的 HiAgent 对话，并返回对话 ID"""
        async with HiAgentClient(api_key=api_key, host=self.endpoint, uid=self.uid) as client:
            response = await client.create_conversation()
            response_data = response.json()

            # 安全地访问嵌套字典
            conversation_data = response_data.get("Conversation", {})
            conversation_id = conversation_data.get("AppConversationID")
            
            if not conversation_id:
                raise FetcherError("未能从响应中获取 AppConversationID")
                
            return conversation_id

    async def send_query(self, conversation_id: str, query: str, agent_name: str) -> QueryResponse:
        """向指定的 HiAgent 对话发送问题，并返回回答"""
        api_key = self._get_key_by_agent_name(agent_name)
            
        async with HiAgentClient(api_key=api_key, host=self.endpoint, uid=self.uid) as client:
            response = await client.send_query(query=query, conversation_id=conversation_id)
            response_data = response.json()
            return QueryResponse.model_validate(response_data)

    async def get_conversation_messages(self, conversation_id: str, agent_name: str, limit: int = 100) -> List[Message]:
        """获取会话历史消息列表，并将其格式化为统一的格式"""
        api_key = self._get_key_by_agent_name(agent_name)
        async with HiAgentClient(api_key=api_key, host=self.endpoint, uid=self.uid) as client:
            response = await client.get_conversation_messages(conversation_id=conversation_id, limit=limit)
            response_data = response.json()
            
            json_messages = response_data.get("Messages", [])
            messages: List[Message] = []
            
            if not isinstance(json_messages, list):
                return []

            for json_message in reversed(json_messages):
                # 如果存在，则添加用户消息
                if json_message.get("Query"):
                    messages.append(Message(
                        id=json_message.get("QueryID"),
                        content=json_message.get("Query"),
                        isUser=True,
                    ))
                
                # 如果存在，则添加助手的回答
                answer_info = json_message.get("AnswerInfo", {})
                if answer_info and answer_info.get("Answer"):
                    messages.append(Message(
                        id=answer_info.get("TaskID"),
                        content=answer_info.get("Answer"),
                        isUser=False,
                    ))
                
            return messages
        
    async def inquiry(self, query: str, conversation_id: str = "") -> QueryResponse:
        """向 HiAgent 发送查询，并返回回答"""
        print(f"查询: {query}, 对话ID: {conversation_id}")
        if not conversation_id:
            print("创建新对话")
            conversation_id = await self.create_conversation("inquiry")
            print(f"新对话ID: {conversation_id}")
        return await self.send_query(conversation_id, query, "inquiry")

    @redis_cache(key_template="predict_probabilities:{bid_info.id}")
    async def predict_probabilities(self, bid_info: BidInfo, company_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """向 HiAgent 发送查询，并获取概率预测结果"""
        bid_id = bid_info.id
        if not bid_id:
            raise ValidationError("标讯信息中缺少必要的 id 字段")
            
        # 创建新对话
        conversation_id = await self.create_conversation("probability")
        query = f"请根据以下标讯信息和公司信息，预测该公司中标的概率。如缺少必要信息，可使用插件搜索。\n\n标讯信息：{bid_info}\n\n公司信息：{company_info}"
        response = await self.send_query(conversation_id, query, "probability")

        try:
            answer = response.answer
                
            probabilities = json.loads(answer).get("probability_predict", [])
            
            if not isinstance(probabilities, list):
                raise ValidationError("返回的概率数据格式不正确")
                
            return probabilities
            
        except json.JSONDecodeError as e:
            raise ValidationError(f"解析 HiAgent 响应失败: {e}")

    @redis_cache(key_template="extract_key_factors:{bid_info.id}")
    async def extract_key_factors(self, bid_info: BidInfo) -> List[Dict[str, Any]]:
        """向 HiAgent 发送查询，并获取关键因素列表"""
        bid_id = bid_info.id
        if not bid_id:
            raise ValidationError("标讯信息中缺少必要的 id 字段")

        conversation_id = await self.create_conversation("key_factor")
        query = f"标讯信息如下：\n\n{bid_info}"
        response = await self.send_query(conversation_id, query, "key_factor")
        
        print(f"关键因素响应: {response}")
        
        try:
            answer = response.answer
            key_factors = json.loads(answer)

            if not isinstance(key_factors, list):
                raise ValidationError("返回的关键因素数据格式不正确")

            return key_factors
            
        except json.JSONDecodeError as e:
            raise ValidationError(f"解析 HiAgent 响应失败: {e}")

# 全局服务实例
agent_service = AgentService()


def get_agent_service() -> AgentService:
    """获取智能对话服务实例"""
    return agent_service

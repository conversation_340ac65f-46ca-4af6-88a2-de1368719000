"""
公司数据模型
"""
from sqlalchemy import Column, String
from app.core.database import Base
from sqlalchemy.orm import relationship

class Company(Base):
    """公司信息表"""
    __tablename__ = "companies"

    id = Column(String(36), primary_key=True, index=True)
    name = Column(String(255), unique=True, index=True, comment="公司名称")
    # 在这里添加更多字段...

    users = relationship("User", back_populates="company")
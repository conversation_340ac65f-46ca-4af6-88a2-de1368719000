import hashlib
import time
from typing import Dict, Any
import httpx
from fastapi import logger

from app.core.config import settings
from app.core.exceptions import FetcherError
from app.utils.cache import redis_cache
from app.schemas.bid import BidInfo, BidListResponse
from app.schemas.criteria import SearchCriteriaContent, map_criteria_to_payload


appid = settings.BID_API_APPID
key = settings.BID_API_KEY
api_url_list = settings.BID_API_URL_LIST
api_url_info = settings.BID_API_URL_INFO
headers = {'Content-Type': 'application/json;charset=utf-8'}

def _generate_token(timestamp: int) -> str:
    """根据 appid, timestamp, 和 key 生成 token"""
    string_to_encrypt = appid + str(timestamp) + key
    token = hashlib.md5(string_to_encrypt.encode('utf-8')).hexdigest().upper()
    return token

async def _call_external_api(url: str, payload: Dict[str, Any]) -> Dict[str, Any]:
    """异步调用外部 API 的通用函数"""
    async with httpx.AsyncClient() as client:
        try:
            response = await client.post(
                url, 
                headers=headers, 
                json=payload,
                timeout=10.0
            )
            response.raise_for_status()
        except httpx.RequestError as e:
            raise FetcherError(f"请求外部 API 失败: {e}") from e

    response_data = response.json()
    
    # 处理外部 API 返回的业务错误
    if response_data.get("code") != 0:
        raise FetcherError(f"外部 API 返回错误: {response_data.get('msg')}")
        
    return response_data

@redis_cache(key_template="bid_list:{criteria}", ttl=60 * 30)
async def query_bids(criteria: SearchCriteriaContent) -> BidListResponse:
    """根据指定的条件异步查询标讯列表"""
    timestamp = int(time.time())
    token = _generate_token(timestamp)

    query_params = map_criteria_to_payload(criteria)
    payload = query_params.model_dump(exclude_unset=True)
    payload.update({
        "appid": appid,
        "token": token,
        "timestamp": str(timestamp),
    })

    resp = await _call_external_api(api_url_list, payload)
    return BidListResponse(**resp)

@redis_cache(key_template="bid_details:{bid_id}", ttl=60 * 60 * 24)
async def get_bid_details(bid_id: str) -> BidInfo | None:
    """根据标讯 ID 异步获取其详细信息"""
    timestamp = int(time.time())
    token = _generate_token(timestamp)

    payload = {
        "appid": appid,
        "token": token,
        "timestamp": str(timestamp),
        "id": bid_id
    }
    
    resp = await _call_external_api(api_url_info, payload)
    if "data" not in resp or not resp["data"]:
        logger.error(f"标讯 ID {bid_id} 的数据不存在或格式错误, 返回数据: {resp}")
        return None
    else:
        return BidInfo(**resp["data"][0])
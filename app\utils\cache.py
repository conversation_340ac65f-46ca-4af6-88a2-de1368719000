"""
通用缓存工具模块
"""
import functools
import json
import inspect
from inspect import signature
from typing import Awaitable, Callable, ParamSpec, TypeVar

from pydantic import TypeAdapter

from app.core.redis import get_redis_client


P = ParamSpec("P")
R = TypeVar("R")


def redis_cache(
    key_template: str, ttl: int | None = None
) -> Callable[[Callable[P, Awaitable[R]]], Callable[P, Awaitable[R]]]:
    """
    一个健壮的、类型安全的 Redis 缓存装饰器。

    Args:
        key_template (str): 缓存键的模板，使用花括号表示参数，如 "user:{user_id}"。
        ttl (int | None, optional): 缓存的过期时间（秒）。如果为 None, 则为持久化缓存。默认为 None。
    """
    def decorator(
        func: Callable[P, Awaitable[R]],
    ) -> Callable[P, Awaitable[R]]:
        # 在装饰器加载时就解析好返回类型，以便后续序列化/反序列化
        try:
            return_type = signature(func).return_annotation
            # 对于 list[Model] 或 Model | None 这种复杂类型，需要用 TypeAdapter
            type_adapter = TypeAdapter(return_type) if return_type != inspect.Signature.empty else None
        except Exception:
            type_adapter = None

        @functools.wraps(func)
        async def wrapper(*args: P.args, **kwargs: P.kwargs) -> R:
            # 1. 生成缓存键
            # 将函数的参数绑定到一个字典中，以便安全地填充模板
            try:
                bound_args = signature(func).bind(*args, **kwargs).arguments
                cache_key = key_template.format(**bound_args)
            except KeyError as e:
                raise TypeError(
                    f"缓存键模板中的占位符 '{e.args[0]}' "
                    f"在函数 '{func.__name__}' 的参数中未找到。"
                )

            redis = await get_redis_client()

            # 2. 查缓存
            cached_data = await redis.get(cache_key)
            if cached_data:
                # 3. 反序列化
                if type_adapter:
                    try:
                        # 使用 Pydantic 的 TypeAdapter 来解析，支持 List[Model], Model 等
                        return type_adapter.validate_json(cached_data)
                    except Exception:
                        # 如果解析失败，可能是不支持的类型，回退到 json.loads
                        return json.loads(cached_data)
                else:
                    # 对于没有类型注解或简单类型的，直接用 json.loads
                    return json.loads(cached_data)

            # 4. 缓存未命中，执行原函数
            result = await func(*args, **kwargs)

            # 5. 序列化并存入缓存
            if result is not None:
                if type_adapter:
                    # 使用 TypeAdapter 来序列化，可以正确处理 Pydantic 模型和列表
                    json_result = type_adapter.dump_json(result).decode('utf-8')
                else:
                    # 对于不支持的类型，使用默认的json.dumps
                    json_result = json.dumps(result, default=str)
                
                await redis.set(cache_key, json_result, ex=ttl)

            return result
        return wrapper
    return decorator
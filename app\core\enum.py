from __future__ import annotations

from typing import List, Optional
from pydantic import BaseModel
from enum import Enum


class EnumMember(BaseModel):
    value: str
    label: str
    children: Optional[List[EnumMember]] = []

class BudgetScopeEnum(str, Enum):
    CUSTOM = "custom"
    ALL = "all"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


BUDGET_SCOPE_ITEMS = [
    EnumMember(value=BudgetScopeEnum.ALL, label="不限"),
    EnumMember(value=BudgetScopeEnum.LOW, label="100万以下"),
    EnumMember(value=BudgetScopeEnum.MEDIUM, label="100-1000万"),
    EnumMember(value=BudgetScopeEnum.HIGH, label="1000万以上"),
    EnumMember(value=BudgetScopeEnum.CUSTOM, label="自定义"),
]

class PublishTimeScopeEnum(str, Enum):
    LAST_DAY = "last_day"
    LAST_WEEK = "last_week"
    LAST_MONTH = "last_month"
    CUSTOM = "custom"
    ALL = "all"


PUBLISH_TIME_SCOPE_ITEMS = [
    EnumMember(value=PublishTimeScopeEnum.ALL, label="不限"),
    EnumMember(value=PublishTimeScopeEnum.LAST_DAY, label="近一天"),
    EnumMember(value=PublishTimeScopeEnum.LAST_WEEK, label="近一周"),
    EnumMember(value=PublishTimeScopeEnum.LAST_MONTH, label="近一月"),
    EnumMember(value=PublishTimeScopeEnum.CUSTOM, label="自定义"),
]


class IndustryEnum(str, Enum):
    ALL = "不限"
    CONSTRUCTION = "建筑工程"
    WATER_CONSERVANCY = "水利水电"
    ENERGY_CHEMICAL = "能源化工"
    WEAK_CURRENT_SECURITY = "弱电安防"
    IT = "信息技术"
    ADMIN_OFFICE = "行政办公"
    MACHINERY = "机械设备"
    TRANSPORTATION = "交通工程"
    HEALTHCARE = "医疗卫生"
    MUNICIPAL = "市政设施"
    SERVICE_PURCHASE = "服务采购"
    AGRICULTURE = "农林牧渔"

INDUSTRY_SUB_CATEGORIES = {
    IndustryEnum.CONSTRUCTION: ["勘察设计", "工程施工", "材料设备", "机电安装", "监理咨询"],
    IndustryEnum.WATER_CONSERVANCY: ["水利工程", "发电工程", "航运工程", "其他工程"],
    IndustryEnum.ENERGY_CHEMICAL: ["新能源", "原材料", "化工产品", "仪器仪表", "设备物资"],
    IndustryEnum.WEAK_CURRENT_SECURITY: ["综合布线", "智能系统", "智能家居"],
    IndustryEnum.IT: ["运维服务", "软件开发", "系统集成及安全", "其他"],
    IndustryEnum.ADMIN_OFFICE: ["办公家具", "通用办公设备", "专业设备", "办公用品", "生活用品"],
    IndustryEnum.MACHINERY: ["矿山机械", "工程机械", "机械零部件", "机床相关", "车辆", "其他机械设备"],
    IndustryEnum.TRANSPORTATION: ["道路", "桥梁", "隧道", "其他", "轨道"],
    IndustryEnum.HEALTHCARE: ["设备", "耗材", "药品"],
    IndustryEnum.MUNICIPAL: ["道路", "绿化", "线路管网", "综合项目"],
    IndustryEnum.SERVICE_PURCHASE: ["法律咨询", "会计", "物业", "审计", "安保", "仓储物流", "广告宣传印刷", "其他"],
    IndustryEnum.AGRICULTURE: ["生产物资", "生产设备", "相关服务"],
}

INDUSTRY_ITEMS = [
    EnumMember(value=industry.value, label=industry.value, children=[
        EnumMember(value=sub_category, label=sub_category) for sub_category in INDUSTRY_SUB_CATEGORIES.get(industry, [])
    ]) for industry in IndustryEnum
]

class RegionEnum(str, Enum):
    ALL = "不限"
    BEIJING = "北京"
    TIANJIN = "天津"
    HEBEI = "河北"
    SHANXI = "山西"
    INNER_MONGOLIA = "内蒙古"
    LIAONING = "辽宁"
    JILIN = "吉林"
    HEILONGJIANG = "黑龙江"
    SHANGHAI = "上海"
    JIANGSU = "江苏"
    ZHEJIANG = "浙江"
    ANHUI = "安徽"
    FUJIAN = "福建"
    JIANGXI = "江西"
    SHANDONG = "山东"
    HENAN = "河南"
    HUBEI = "湖北"
    HUNAN = "湖南"
    GUANGDONG = "广东"
    GUANGXI = "广西"
    HAINAN = "海南"
    CHONGQING = "重庆"
    SICHUAN = "四川"
    GUIZHOU = "贵州"
    YUNNAN = "云南"
    TIBET = "西藏"
    SHAANXI = "陕西"
    GANSU = "甘肃"
    QINGHAI = "青海"
    NINGXIA = "宁夏"
    XINJIANG = "新疆"
    TAIWAN = "台湾"
    HONG_KONG = "香港"
    MACAO = "澳门"

REGION_ITEMS = [
    EnumMember(value=region.value, label=region.value) for region in RegionEnum
]
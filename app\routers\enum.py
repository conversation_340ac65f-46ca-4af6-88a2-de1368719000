from fastapi import APIRouter
from app.core.enum import (
    BUDGET_SCOPE_ITEMS,
    PUBLISH_TIME_SCOPE_ITEMS,
    REGION_ITEMS,
    INDUSTRY_ITEMS
)
from app.utils.response import success_response

router = APIRouter(prefix="/enums", tags=["枚举值"])


@router.get("/", summary="获取所有枚举值")
def get_enums():
    """
    提供给前端用于获取各类枚举值的接口，格式为：
    interface DataItem {
      label: string;
      value: any;
      children?: DataItem[];
    }
    """

    return success_response(
        data = {
            "budget_scopes": BUDGET_SCOPE_ITEMS,
            "publish_time_scopes": PUBLISH_TIME_SCOPE_ITEMS,
            "industries": INDUSTRY_ITEMS,
            "regions": REGION_ITEMS,
        }
    )
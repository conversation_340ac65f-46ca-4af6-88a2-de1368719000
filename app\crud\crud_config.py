from redis.asyncio import Redis


def set_user_config(
    user_id: str,
    config_data: dict,
    redis_client: Redis
) -> None:
    """
    设置用户配置项。

    :param user_id: 用户ID
    :param config_key: 配置项键
    :param config_value: 配置项值
    """
    redis_client.set(user_config_key(user_id), config_data)

def init_user_config(
    user_id: str,
    redis_client: Redis
) -> None:
    """
    初始化用户配置。

    :param user_id: 用户ID
    :param config_data: 配置数据字典
    """
    redis_client.set(
        user_config_key(user_id),
        {
            "track_bid": True,  # 默认跟踪标讯
            "track_criteria": True,  # 默认跟踪条件
            "notification": True,  # 默认开启通知
            "theme": "light",  # 默认主题
        }
    )

def get_user_config(
    user_id: str,
    redis_client: Redis
) -> dict | None:
    """
    获取用户配置项。

    :param user_id: 用户ID
    :return: 用户配置项的字典，如果不存在则返回None
    """
    return redis_client.get(user_config_key(user_id))

def user_config_key(user_id: str) -> str:
    """
    生成用户配置项的 Redis 键。

    :param user_id: 用户ID
    :return: 用户配置项的 Redis 键
    """
    return f"user_config:{user_id}"
# mail_client.py

import asyncio
from typing import Dict, List, Optional
import aiosmtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from logging import getLogger
from app.core.config import settings

logger = getLogger(__name__)

class MailClient:
    # _client 现在是实例属性，用 Optional 表示它可能为 None
    _client: Optional[aiosmtplib.SMTP]

    def __init__(self):
        """
        初始化时只设置配置，不进行连接。
        """
        self._client = None
        self.smtp_config = {
            "hostname": settings.SENDER_SMTP_SERVER,
            "port": settings.SENDER_SMTP_PORT,
            "use_tls": True,  # 推荐在 connect 时指定，更清晰
            "username": settings.SENDER_EMAIL,
            "password": str(settings.SENDER_PASSWORD),
            "timeout": 10
        }

    async def connect(self):
        """
        显式建立连接和登录。应该在应用启动时调用。
        """
        if self._client and self._client.is_connected:
            logger.info("SMTP client is already connected.")
            return

        logger.info("Connecting to SMTP server...")
        self._client = aiosmtplib.SMTP(**self.smtp_config)
        try:
            await self._client.connect()
            # login 会自动处理 STARTTLS
            # await self._client.login(self.smtp_config["username"], self.smtp_config["password"])
            logger.info("SMTP client connected successfully.")
        except aiosmtplib.SMTPException as e:
            logger.error(f"Failed to connect to SMTP server: {e}")
            self._client = None # 连接失败，重置client
            raise  # 重新抛出异常，让应用启动失败

    async def close(self):
        """
        显式关闭连接。应该在应用关闭时调用。
        """
        if self._client and self._client.is_connected:
            await self._client.quit()
            logger.info("SMTP connection closed.")
        self._client = None

    async def _ensure_connected(self):
        """
        确保客户端已连接，如果未连接或连接丢失，则尝试重新连接。
        """
        is_connected = False
        if self._client:
            # 使用 noop() 心跳来检查连接是否仍然有效
            try:
                # noop() 是一个轻量级的检查，如果连接断开会抛出异常
                await self._client.noop()
                is_connected = True
            except aiosmtplib.SMTPServerDisconnected:
                logger.warning("SMTP server disconnected. Reconnecting...")
                is_connected = False
            except Exception:
                 # 其他网络错误
                 is_connected = False


        if not is_connected:
            await self.connect()

    async def send(self, subject: str, to: List[str], body: str, **headers: Dict[str, str]):
        """
        发送邮件，发送前会确保连接有效。
        """
        # 确保连接，如果失败，connect()会抛出异常
        await self._ensure_connected()

        if not self._client:
            logger.error("Cannot send email, SMTP client is not available.")
            return

        msg = MIMEMultipart("alternative")
        msg["Subject"] = subject
        msg["From"] = settings.SENDER_EMAIL
        msg["To"] = ", ".join(to)  # To 头部应该是逗号分隔的字符串
        for k, v in headers.items():
            msg[k] = v
        msg.attach(MIMEText(body, "html", "utf-8"))

        try:
            await self._client.send_message(msg)
            logger.info(f"Mail sent successfully to {to}")
        except aiosmtplib.SMTPException as e:
            logger.error(f"Failed to send email: {e}")
            # 发送失败后，可以选择重置连接，以便下次强制重连
            await self.close()
            raise # 重新抛出，让调用者知道失败了

# --- 单例模式实现 ---

# 在模块级别创建一个实例
mail_client = MailClient()

def get_mail_client() -> MailClient:
    """
    获取邮件客户端的全局单例。
    :return: MailClient 实例
    """
    return mail_client

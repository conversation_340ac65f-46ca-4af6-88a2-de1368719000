from .types import *
from .handler import base_api_exception_handler, fetcher_exception_handler, generic_exception_handler, validation_exception_handler
from fastapi import FastAPI
from fastapi.exceptions import RequestValidationError


def register_exception_handlers(app: FastAPI):
    app.add_exception_handler(BaseAP<PERSON><PERSON><PERSON>, base_api_exception_handler) # type: ignore
    app.add_exception_handler(<PERSON>tcherError, fetcher_exception_handler) # type: ignore
    app.add_exception_handler(Exception, generic_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler) # type: ignore
#!/usr/bin/env python3
"""
FastAPI 标讯数据接口服务启动脚本
"""
import sys
import argparse
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def main():
    parser = argparse.ArgumentParser(description="启动 FastAPI 标讯数据接口服务")
    parser.add_argument(
        "--host", 
        default="127.0.0.1", 
        help="服务器主机地址 (默认: 127.0.0.1)"
    )
    parser.add_argument(
        "--port", 
        type=int, 
        default=8000, 
        help="服务器端口 (默认: 8000)"
    )
    parser.add_argument(
        "--reload", 
        action="store_true", 
        help="启用自动重载 (开发模式)"
    )
    parser.add_argument(
        "--workers", 
        type=int, 
        default=1, 
        help="工作进程数量 (默认: 1)"
    )
    parser.add_argument(
        "--log-level", 
        default="info", 
        choices=["critical", "error", "warning", "info", "debug", "trace"],
        help="日志级别 (默认: info)"
    )
    
    args = parser.parse_args()
    
    # 检查环境变量文件
    env_file = project_root / ".env"
    if not env_file.exists():
        env_example = project_root / ".env.example"
        if env_example.exists():
            print(f"警告: 未找到 .env 文件，请复制 {env_example} 为 .env 并配置相应参数")
        else:
            print("警告: 未找到环境配置文件")
    
    # 导入并启动应用
    try:
        import uvicorn
        from app.main import app
        
        print(f"启动 FastAPI 标讯数据接口服务...")
        print(f"服务地址: http://{args.host}:{args.port}")
        print(f"API 文档: http://{args.host}:{args.port}/docs")
        print(f"ReDoc 文档: http://{args.host}:{args.port}/redoc")
        print(f"健康检查: http://{args.host}:{args.port}/health")
        
        # 配置重载排除的文件和目录
        reload_excludes = []
        if args.reload:
            # 默认排除项
            reload_excludes = [
                "logs/*",
                "*.log",
                "logs/**/*",
                "__pycache__/*",
                ".git/*",
                ".pytest_cache/*",
                "*.pyc"
                ".venv/*"
            ]

            # 如果存在 .reloadignore 文件，读取其中的排除项
            reload_ignore_file = project_root / ".reloadignore"
            if reload_ignore_file.exists():
                with open(reload_ignore_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#'):
                            reload_excludes.append(line)

        uvicorn.run(
            "app.main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            reload_excludes=reload_excludes,
            workers=args.workers if not args.reload else 1,
            log_level=args.log_level,
            access_log=True
        )
        
    except ImportError as e:
        print(f"导入错误: {e.with_traceback()}")
        print("请确保已安装所有依赖: pip install -r requirements.txt")
        sys.exit(1)
    except Exception as e:
        print(f"启动失败: {e.with_traceback()}")
        sys.exit(1)


if __name__ == "__main__":
    main()

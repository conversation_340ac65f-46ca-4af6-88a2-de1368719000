"""
中间件模块
"""
import time
import uuid
from typing import Callable

from fastapi import Request, Response, FastAPI
from fastapi.middleware.cors import CORSMiddleware
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.config import settings
from app.core.logging import get_logger


class RequestIDMiddleware(BaseHTTPMiddleware):
    """请求ID中间件，为每个请求生成唯一ID"""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = get_logger("middleware.request_id")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 将请求ID添加到请求状态中
        request.state.request_id = request_id
        
        # 处理请求
        response = await call_next(request)
        
        # 将请求ID添加到响应头中
        response.headers["X-Request-ID"] = request_id
        
        return response


class ProcessTimeMiddleware(BaseHTTPMiddleware):
    """处理时间中间件，记录请求处理时间"""
    
    def __init__(self, app):
        super().__init__(app)
        self.logger = get_logger("middleware.process_time")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # 处理请求
        response = await call_next(request)
        
        # 计算处理时间
        process_time = time.time() - start_time
        
        # 添加处理时间到响应头
        response.headers["X-Process-Time"] = str(process_time)
        
        # 记录慢请求
        if process_time > 1.0:  # 超过1秒的请求
            self.logger.warning(
                f"Slow request: {request.method} {request.url.path} "
                f"took {process_time:.2f}s"
            )
        
        return response


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件，添加安全相关的HTTP头"""
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 添加安全头
        response.headers.update({
            "X-Content-Type-Options": "nosniff",
            "X-Frame-Options": "DENY",
            "X-XSS-Protection": "1; mode=block",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            # "Content-Security-Policy": "default-src 'self'",
        })
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """简单的速率限制中间件"""
    
    def __init__(self, app, max_requests: int = 100, window_seconds: int = 60):
        super().__init__(app)
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = {}  # 简单的内存存储，生产环境应使用Redis
        self.logger = get_logger("middleware.rate_limit")
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 获取客户端IP
        client_ip = self._get_client_ip(request)
        current_time = time.time()
        
        # 清理过期记录
        self._cleanup_expired_records(current_time)
        
        # 检查速率限制
        if client_ip in self.requests:
            request_times = self.requests[client_ip]
            # 过滤窗口期内的请求
            recent_requests = [
                req_time for req_time in request_times
                if current_time - req_time < self.window_seconds
            ]
            
            if len(recent_requests) >= self.max_requests:
                self.logger.warning(f"Rate limit exceeded for IP: {client_ip}")
                return Response(
                    content="Rate limit exceeded",
                    status_code=429,
                    headers={"Retry-After": str(self.window_seconds)}
                )
            
            # 更新请求记录
            recent_requests.append(current_time)
            self.requests[client_ip] = recent_requests
        else:
            # 新IP，记录第一次请求
            self.requests[client_ip] = [current_time]
        
        return await call_next(request)
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查 X-Forwarded-For 头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        # 检查 X-Real-IP 头
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 使用客户端地址
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def _cleanup_expired_records(self, current_time: float):
        """清理过期的请求记录"""
        expired_ips = []
        for ip, request_times in self.requests.items():
            # 过滤掉过期的请求
            recent_requests = [
                req_time for req_time in request_times
                if current_time - req_time < self.window_seconds
            ]
            
            if recent_requests:
                self.requests[ip] = recent_requests
            else:
                expired_ips.append(ip)
        
        # 删除没有近期请求的IP记录
        for ip in expired_ips:
            del self.requests[ip]


def register_middleware(app: FastAPI):
    # 添加自定义中间件（按执行顺序添加）
    # app.add_middleware(RequestIDMiddleware)
    # app.add_middleware(ProcessTimeMiddleware)
    # app.add_middleware(SecurityHeadersMiddleware)

    # 配置 CORS 中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
        allow_methods=settings.CORS_ALLOW_METHODS,
        allow_headers=settings.CORS_ALLOW_HEADERS,
    )

    # 添加速率限制中间件（可选，根据需要启用）
    if not settings.DEBUG:
        app.add_middleware(RateLimitMiddleware, max_requests=100, window_seconds=60)
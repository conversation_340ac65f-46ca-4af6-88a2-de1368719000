"""
HiAgent相关的Pydantic模型
"""
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field
from app.schemas.bid import BidInfo


class QueryResponse(BaseModel):
    """HiAgent查询响应模型"""
    task_id: str = Field(..., description="任务ID")
    id: str = Field(..., description="回答ID")
    conversation_id: str = Field(..., description="对话ID")
    answer: str = Field(..., description="回答内容")
    suggested_questions: List[str] = Field([], description="推荐问题列表")


class Message(BaseModel):
    """消息模型"""
    id: str = Field(..., description="消息ID")
    content: str = Field(..., description="消息内容")
    isUser: bool = Field(..., description="是否为用户消息")


class InquiryPayload(BaseModel):
    """智能问询请求模型"""
    query: str = Field(..., description="用户提出的问题")
    conversation_id: str = Field("", description="对话ID")


class PredictionPayload(BaseModel):
    """中标概率预测请求模型"""
    bid_info: BidInfo = Field(..., description="标讯信息")
    company_info: Dict[str, Any] = Field(..., description="公司信息")


class KeyFactorsPayload(BaseModel):
    """关键因素提取请求模型"""
    bid_info: BidInfo = Field(..., description="标讯信息")

# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o requirements.txt
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via starlette
click==8.2.1
    # via uvicorn
colorama==0.4.6
    # via click
fastapi==0.116.1
    # via bid-helper-backend (pyproject.toml)
greenlet==3.2.3
    # via sqlalchemy
h11==0.16.0
    # via uvicorn
idna==3.10
    # via anyio
pydantic==2.11.7
    # via
    #   fastapi
    #   pydantic-settings
pydantic-core==2.33.2
    # via pydantic
pydantic-settings==2.10.1
    # via bid-helper-backend (pyproject.toml)
python-dotenv==1.1.1
    # via pydantic-settings
redis==6.2.0
    # via bid-helper-backend (pyproject.toml)
sniffio==1.3.1
    # via anyio
sqlalchemy==2.0.41
    # via bid-helper-backend (pyproject.toml)
starlette==0.47.1
    # via fastapi
typing-extensions==4.14.1
    # via
    #   fastapi
    #   pydantic
    #   pydantic-core
    #   sqlalchemy
    #   typing-inspection
typing-inspection==0.4.1
    # via
    #   pydantic
    #   pydantic-settings
uvicorn==0.35.0
    # via bid-helper-backend (pyproject.toml)

from sqlalchemy.orm import Session
import uuid

from app.models.criteria import SearchCriteria
from app.schemas.criteria import SearchCriteriaCreate


def get_criteria_by_id(db: Session, criteria_id: str) -> SearchCriteria | None:
    return db.query(SearchCriteria).filter(SearchCriteria.id == criteria_id).first()

def get_criteria_by_user_id(db: Session, user_id: str) -> list[SearchCriteria]:
    return db.query(SearchCriteria).filter(SearchCriteria.user_id == user_id).all()

def add_criteria(db: Session, criteria: SearchCriteriaCreate):
    db_criteria = SearchCriteria(id=str(uuid.uuid4()), **criteria.model_dump(exclude={'next', 'page', 'size'}))
    db.add(db_criteria)
    db.commit()
    db.refresh(db_criteria)
    return db_criteria

def delete_criteria(db: Session, criteria_id: str):
    criteria = db.query(SearchCriteria).filter(SearchCriteria.id == criteria_id).first()
    if not criteria:
        raise ValueError("Criteria not found")
    db.delete(criteria)
    db.commit()

def toggle_track_criteria(db: Session, criteria_id: str):
    criteria = db.query(SearchCriteria).filter(SearchCriteria.id == criteria_id).first()
    if not criteria:
        raise ValueError("Criteria not found")
    is_tracked = bool(criteria.is_tracked)
    setattr(criteria, "is_tracked", not is_tracked)

    db.add(criteria)
    db.commit()
    db.refresh(criteria)
    return criteria
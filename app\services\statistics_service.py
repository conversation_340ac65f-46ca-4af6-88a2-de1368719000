"""
统计分析服务
"""
from typing import Dict, Any

from app.schemas.criteria import BidQueryPayload
from app.services.bid_service import get_bid_service


class StatisticsService:
    """统计分析服务类"""
    
    def __init__(self):
        self.bid_service = get_bid_service()

    async def get_company_partners(self, company_name: str) -> Dict[str, int]:
        """
        获取指定公司的合作伙伴信息。
        
        :param company_name: 公司名称
        :return: 合作伙伴信息字典，键为公司名称，值为合作次数
        """
        partners = {}

        # 查询该公司作为采购单位的项目，获取中标单位作为合作伙伴
        has_more = True
        next_cursor = 0
        
        while has_more:
            response = await self.bid_service.query_bids(BidQueryPayload(  # type: ignore
                keyword=company_name,
                keywordScope="采购单位",
                next=next_cursor,
            ))
            
            print(f"采购单位查询响应: {response}")
            
            if response and "data" in response and response["data"] is not None:
                for bid_data in response["data"]:
                    if "s_winner" in bid_data and bid_data["s_winner"]:
                        winner_name = bid_data["s_winner"]
                        if winner_name not in partners:
                            partners[winner_name] = 1
                        else:
                            partners[winner_name] += 1
            
            next_cursor = response.get("next", 0)
            print(f"下一页游标: {next_cursor}, 当前页大小: {response.get('size', 0)}, 总数: {response.get('count', 0)}")
            
            if next_cursor == 0:
                has_more = False
        
        # 查询该公司作为中标单位的项目，获取采购单位作为合作伙伴
        has_more = True
        next_cursor = 0
        
        while has_more:
            response = await self.bid_service.query_bids(BidQueryPayload(  # type: ignore
                keyword=company_name,
                keywordScope="中标单位",
                next=next_cursor,
            ))
            
            print(f"中标单位查询响应: {response}")
            
            if response and "data" in response and response["data"] is not None:
                for bid_data in response["data"]:
                    if "buyer" in bid_data and bid_data["buyer"]:
                        buyer_name = bid_data["buyer"]
                        if buyer_name not in partners:
                            partners[buyer_name] = 1
                        else:
                            partners[buyer_name] += 1

            next_cursor = response.get("next", 0)
            print(f"下一页游标: {next_cursor}, 当前页大小: {response.get('size', 0)}, 总数: {response.get('count', 0)}")

            if next_cursor == 0:
                has_more = False

        # 对合作伙伴按数量排序
        sorted_partners = sorted(partners.items(), key=lambda x: x[1], reverse=True)
        print(f"合作伙伴排序结果: {sorted_partners}")

        return dict(sorted_partners)

    async def get_company_bid_statistics(self, company_name: str) -> Dict[str, Any]:
        """
        获取公司投标统计信息
        
        :param company_name: 公司名称
        :return: 投标统计信息
        """
        # 获取作为采购单位的项目统计
        tender_response = await self.bid_service.search_company_tenders(company_name, page=1, size=1)
        tender_count = tender_response.get("count", 0)
        
        # 获取作为中标单位的项目统计
        win_response = await self.bid_service.search_company_wins(company_name, page=1, size=1)
        win_count = win_response.get("count", 0)
        
        # 计算中标率
        win_rate = (win_count / tender_count * 100) if tender_count > 0 else 0
        
        return {
            "company_name": company_name,
            "tender_count": tender_count,  # 投标项目数量
            "win_count": win_count,        # 中标项目数量
            "win_rate": round(win_rate, 2), # 中标率（百分比）
        }

    async def get_industry_statistics(self, industry: str, limit: int = 100) -> Dict[str, Any]:
        """
        获取行业统计信息
        
        :param industry: 行业分类
        :param limit: 返回结果数量限制
        :return: 行业统计信息
        """
        response = await self.bid_service.query_bids(BidQueryPayload(  # type: ignore
            industry=industry,
            next=0
        ))
        
        if not response or "data" not in response:
            return {
                "industry": industry,
                "total_projects": 0,
                "companies": [],
                "avg_budget": 0
            }
        
        projects = response.get("data", [])
        total_projects = response.get("count", 0)
        
        # 统计公司参与情况
        company_stats = {}
        total_budget = 0
        budget_count = 0
        
        for project in projects[:limit]:
            # 统计采购单位
            if "buyer" in project and project["buyer"]:
                buyer = project["buyer"]
                if buyer not in company_stats:
                    company_stats[buyer] = {"as_buyer": 0, "as_winner": 0}
                company_stats[buyer]["as_buyer"] += 1
            
            # 统计中标单位
            if "s_winner" in project and project["s_winner"]:
                winner = project["s_winner"]
                if winner not in company_stats:
                    company_stats[winner] = {"as_buyer": 0, "as_winner": 0}
                company_stats[winner]["as_winner"] += 1
            
            # 统计预算
            if "budget" in project and project["budget"]:
                try:
                    budget = float(project["budget"])
                    total_budget += budget
                    budget_count += 1
                except (ValueError, TypeError):
                    pass
        
        # 计算平均预算
        avg_budget = total_budget / budget_count if budget_count > 0 else 0
        
        # 按参与项目总数排序公司
        sorted_companies = sorted(
            [
                {
                    "company_name": name,
                    "as_buyer": stats["as_buyer"],
                    "as_winner": stats["as_winner"],
                    "total_projects": stats["as_buyer"] + stats["as_winner"]
                }
                for name, stats in company_stats.items()
            ],
            key=lambda x: x["total_projects"],
            reverse=True
        )
        
        return {
            "industry": industry,
            "total_projects": total_projects,
            "companies": sorted_companies[:20],  # 返回前20名公司
            "avg_budget": round(avg_budget, 2)
        }


# 全局服务实例
statistics_service = StatisticsService()


def get_statistics_service() -> StatisticsService:
    """获取统计分析服务实例"""
    return statistics_service

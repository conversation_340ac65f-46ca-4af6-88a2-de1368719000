from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime, timedelta

from app.core.exceptions import ValidationError
from app.core.enum import PublishTimeScopeEnum as TimeEnum, BudgetScopeEnum as BudgetEnum, IndustryEnum, RegionEnum


class SearchCriteriaContent(BaseModel):
    keywords: str | None = Field(None, description="搜索关键词")
    keyword_scope: str | None = Field(None,description="关键词搜索范围，多个用逗号隔开")
    budget_start: int | None = Field(None, description="预算金额范围（开始）")
    budget_end: int | None = Field(None, description="预算金额范围（结束）")
    budget_scope: str | None = Field(None, description="预算金额范围, custom表示自定义范围")
    publish_time_start: datetime | None = Field(None, description="发布时间范围（开始）")
    publish_time_end: datetime | None = Field(None, description="发布时间范围（结束）")
    publish_time_scope: str | None = Field(None, description="发布时间范围, last_day表示最近一天, last_week表示最近一周, last_month表示最近一个月, custom表示自定义时间范围, all表示不限制")
    first_tier_industry: str | None = Field(None, description="一级行业分类")
    second_tier_industry: str | None = Field(None, description="二级行业分类")
    region: str | None = Field(None, description="地区，多个用逗号隔开")
    top_type: str | None = Field(None, description="一级公告类型")
    sub_type: str | None = Field(None, description="二级公告类型")

    next: int = Field(0, description="游标")
    page: int | None = Field(None, description="页码（兼容性字段）")
    size: int | None = Field(None, description="每页数量（兼容性字段）")


class SearchCriteriaCreate(SearchCriteriaContent):
    name: str = Field(..., description="搜索条件名称")

    user_id: str = Field("", description="用户ID")


class SearchCriteriaOut(SearchCriteriaCreate):
    id: str = Field(..., description="搜索条件ID")
    is_tracked: bool = Field(..., description="是否已订阅")
    selected_at: datetime = Field(..., description="上次选中时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True
        populate_by_name = True


class BidQueryPayload(BaseModel):
    """剑鱼：标讯查询请求模型"""
    keyword: Optional[str] = Field(None, description="搜索关键词")
    keywordScope: Optional[str] = Field(None, description="关键词搜索范围，多个用逗号隔开")
    publishtimeStart: Optional[int] = Field(None, description="发布时间范围（开始），10位时间戳")
    publishtimeEnd: Optional[int] = Field(None, description="发布时间范围（结束），10位时间戳")
    budgetStart: Optional[int] = Field(None, description="预算金额范围（开始）")
    budgetEnd: Optional[int] = Field(None, description="预算金额范围（结束）")
    bidamountStart: Optional[int] = Field(None, description="中标金额范围（开始）")
    bidamountEnd: Optional[int] = Field(None, description="中标金额范围（结束）")
    area: Optional[str] = Field(None, description="地域，多个用逗号隔开")
    buyerclass: Optional[str] = Field(None, description="采购单位类型，多个用逗号隔开")
    subType: Optional[str] = Field(None, description="公告子类型，多个用逗号隔开")
    industry: Optional[str] = Field(None, description="行业分类")
    next: int = Field(0, description="游标")
    page: Optional[int] = Field(None, description="页码（兼容性字段）")
    size: Optional[int] = Field(None, description="每页数量（兼容性字段）")


def map_criteria_to_payload(criteria: SearchCriteriaContent) -> BidQueryPayload:
    """将 SearchCriteriaOut 映射到查询请求体"""
    if not criteria.keyword_scope:
        criteria.keyword_scope = "公告标题,公告正文,标的物,项目名称,采购单位,中标单位"


    if criteria.region == RegionEnum.ALL:
        criteria.region = None
    if criteria.first_tier_industry == IndustryEnum.ALL:
        criteria.first_tier_industry = None

    industry = criteria.first_tier_industry+"_"+criteria.second_tier_industry if criteria.first_tier_industry and criteria.second_tier_industry else None
    
    if criteria.publish_time_scope:
        match criteria.publish_time_scope:
            case TimeEnum.LAST_DAY:
                criteria.publish_time_start = datetime.now() - timedelta(days=1)
                criteria.publish_time_end = datetime.now()
            case TimeEnum.LAST_WEEK:
                criteria.publish_time_start = datetime.now() - timedelta(weeks=1)
                criteria.publish_time_end = datetime.now()
            case TimeEnum.LAST_MONTH:
                criteria.publish_time_start = datetime.now() - timedelta(days=30)
                criteria.publish_time_end = datetime.now()
            case TimeEnum.CUSTOM:
                # 保持自定义时间范围
                if not criteria.publish_time_start or not criteria.publish_time_end:
                    raise ValidationError("自定义时间范围必须提供开始和结束时间")
            case _:
                criteria.publish_time_start = None
                criteria.publish_time_end = None
    
    if criteria.budget_scope:
        match criteria.budget_scope:
            case BudgetEnum.LOW:
                criteria.budget_start = 0
                criteria.budget_end = 1000000
            case BudgetEnum.MEDIUM:
                criteria.budget_start = 1000000
                criteria.budget_end = 10000000
            case BudgetEnum.HIGH:
                criteria.budget_start = 10000000
                criteria.budget_end = None
            case BudgetEnum.CUSTOM:
                # 保持自定义预算范围
                if criteria.budget_start is None or criteria.budget_end is None:
                    raise ValidationError("自定义预算范围必须提供开始和结束金额")
            case _:
                criteria.budget_start = None
                criteria.budget_end = None
        
    payload = BidQueryPayload(
        keyword=criteria.keywords,
        keywordScope=criteria.keyword_scope,
        budgetStart=criteria.budget_start,
        budgetEnd=criteria.budget_end,
        publishtimeStart=int(criteria.publish_time_start.timestamp()) if criteria.publish_time_start else None,
        publishtimeEnd=int(criteria.publish_time_end.timestamp()) if criteria.publish_time_end else None,
        industry=industry,
        area=criteria.region,
        subType=criteria.sub_type
    )
    print(f"Mapped criteria to payload: {payload.model_dump()}")
    return payload
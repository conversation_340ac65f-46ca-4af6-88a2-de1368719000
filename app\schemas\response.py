"""
统一API响应模型
"""
from typing import TypeVar, Generic, Optional, Any
from pydantic import BaseModel, Field

# 定义泛型类型
T = TypeVar('T')


class ApiResponse(BaseModel, Generic[T]):
    """
    统一API响应格式
    
    Args:
        code: 响应状态码，0表示成功，非0表示失败
        message: 响应消息
        data: 响应数据，支持泛型
    """
    code: int = Field(..., description="响应状态码，0表示成功，非0表示失败")
    message: str = Field(..., description="响应消息")
    data: Optional[T] = Field(None, description="响应数据")

    class Config:
        json_encoders = {
            # 可以在这里添加自定义的JSON编码器
        }


class SuccessResponse(ApiResponse[T]):
    """成功响应模型"""
    code: int = Field(0, description="成功状态码")
    message: str = Field("success", description="成功消息")


class ErrorResponse(ApiResponse[None]):
    """错误响应模型"""
    code: int = Field(..., description="错误状态码")
    message: str = Field(..., description="错误消息")
    data: Optional[Any] = Field(None, description="错误详情")


class PaginatedResponse(BaseModel, Generic[T]):
    """
    分页响应数据模型
    """
    items: list[T] = Field(..., description="数据列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    size: int = Field(..., description="每页数量")
    pages: int = Field(..., description="总页数")


class PaginatedApiResponse(ApiResponse[PaginatedResponse[T]]):
    """分页API响应模型"""
    pass


# 常用的响应状态码
class ResponseCode:
    """响应状态码常量"""
    SUCCESS = 0
    INVALID_PARAMS = 422
    UNAUTHORIZED = 401
    FORBIDDEN = 403
    NOT_FOUND = 404
    INTERNAL_ERROR = 500
    EXTERNAL_API_ERROR = 502
    SERVICE_UNAVAILABLE = 503


# 常用的响应消息
class ResponseMessage:
    """响应消息常量"""
    SUCCESS = "操作成功"
    INVALID_PARAMS = "参数错误"
    UNAUTHORIZED = "未授权"
    FORBIDDEN = "禁止访问"
    NOT_FOUND = "资源不存在"
    INTERNAL_ERROR = "内部服务器错误"
    EXTERNAL_API_ERROR = "外部API调用失败"
    SERVICE_UNAVAILABLE = "服务不可用"

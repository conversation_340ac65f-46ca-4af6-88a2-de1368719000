from ast import List
from fastapi import APIRouter
from typing import Annotated, List

from app.schemas.response import ApiResponse
from app.schemas.criteria import SearchCriteriaCreate, SearchCriteriaOut
from app.services.criteria_service import CriteriaService, get_criteria_service
from fastapi import Depends
from app.utils.response import success_response
from app.core.security import get_current_user_id


criteria_router = APIRouter(prefix="/subscribe/criteria", tags=["订阅条件"])
bid_router = APIRouter(prefix="/subscribe/bid", tags=["订阅标的"])


@criteria_router.get("/", summary="获取订阅列表", response_model=ApiResponse[List[SearchCriteriaOut]])
async def get_criteria_list(
    user_id: Annotated[str, Depends(get_current_user_id)],
    service: CriteriaService = Depends(get_criteria_service),
):
    return success_response(service.get_criteria_by_user_id(user_id))


@criteria_router.post("/", summary="添加订阅条件", response_model=ApiResponse[SearchCriteriaOut])
async def add_criteria(
    user_id: Annotated[str, Depends(get_current_user_id)],
    criteria: SearchCriteriaCreate,
    service: CriteriaService = Depends(get_criteria_service),
):
    criteria.user_id = user_id
    return success_response(service.add_criteria(criteria))


@criteria_router.delete("/{criteria_id}", summary="删除订阅条件", response_model=ApiResponse[None])
async def delete_criteria(
    criteria_id: str,
    service: CriteriaService = Depends(get_criteria_service),
):
    return success_response(service.delete_criteria(criteria_id))


@criteria_router.put("/{criteria_id}/track", summary="追踪订阅条件", response_model=ApiResponse[SearchCriteriaOut])
async def update_criteria(
    criteria_id: str,
    service: CriteriaService = Depends(get_criteria_service),
):
    return success_response(await service.toggle_track_criteria(criteria_id))
"""
公司相关API路由
"""
from fastapi import APIRouter, Path, Query, Depends

from app.services.company_service import CompanyDataService, get_company_service
from app.utils.response import success_response
from app.schemas.response import ApiResponse

router = APIRouter(prefix="/company", tags=["公司数据"])


@router.get("/info/{keyword}", summary="获取公司基本信息", response_model=ApiResponse[dict])
async def get_company_info(
    keyword: str = Path(..., description="公司名称关键字"),
    service: CompanyDataService = Depends(get_company_service)
):
    """
    根据关键字获取公司的基本信息 (对应 economic/897)。

    参数:
    - keyword: 公司名称关键字

    返回:
    - 公司基本信息数据
    """
    print(f"获取公司基本信息: {keyword}")
    data = await service.get_company_info(keyword)
    return success_response(data=data, message="获取公司基本信息成功")


@router.get("/patents/{keyword}", summary="获取公司专利列表", response_model=ApiResponse[dict])
async def get_patent_list(
    keyword: str = Path(..., description="公司名称关键字"),
    page_num: int = Query(1, description="页码，默认为 1"),
    page_size: int = Query(20, description="每页数量，默认为 20")
):
    """
    获取公司的专利列表信息 (对应 economic/945)。

    参数:
    - keyword: 公司名称关键字
    - page_num: 页码，默认为 1
    - page_size: 每页数量，默认为 20

    返回:
    - 公司专利列表数据
    """
    service = get_company_service()
    data = await service.get_patent_list(keyword, page_num, page_size)
    return success_response(data=data, message="获取公司专利列表成功")


@router.get("/patent/detail/{application_number}", summary="获取专利详情", response_model=ApiResponse[dict])
async def get_patent_detail(
    application_number: str = Path(..., description="专利申请号"),
    service: CompanyDataService = Depends(get_company_service)
):
    """
    根据申请号获取单个专利的详细信息 (对应 economic/943)。

    参数:
    - application_number: 专利申请号

    返回:
    - 专利详细信息数据
    """
    data = await service.get_patent_detail(application_number)
    return success_response(data=data, message="获取专利详情成功")

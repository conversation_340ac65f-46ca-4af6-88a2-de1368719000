from redis.asyncio import <PERSON><PERSON>


def get_redis_key(user_id: str) -> str:
    """
    生成用户跟踪标讯的 Redis 键。
    
    参数:
    - user_id: 用户 ID
    """
    return f"bid_tracked:{user_id}"

async def track_bid(user_id: str, bid_id: str, redis_client: Redis) -> bool:
    """
    跟踪或取消跟踪指定的标讯。
    
    参数:
    - user_id: 用户 ID
    - bid_id: 标讯 ID
    - track_status: 是否跟踪标讯
    """
    return await redis_client.sadd(get_redis_key(user_id), bid_id) > 0

async def untrack_bid(user_id: str, bid_id: str, redis_client: Redis) -> bool:
    """
    取消跟踪指定的标讯。
    
    参数:
    - user_id: 用户 ID
    - bid_id: 标讯 ID
    """
    return await redis_client.srem(get_redis_key(user_id), bid_id) > 0

async def get_tracked_bid_ids(user_id: str, redis_client: Redis) -> set[str]:
    """
    获取用户跟踪的标讯 ID 列表。
    
    参数:
    - user_id: 用户 ID
    """
    return await redis_client.smembers(get_redis_key(user_id))

async def is_bid_tracked(user_id: str, bid_id: str, redis_client: Redis) -> bool:
    """
    检查用户是否跟踪指定的标讯。

    参数:
    - user_id: 用户 ID
    - bid_id: 标讯 ID
    """
    return await redis_client.sismember(get_redis_key(user_id), bid_id)

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Commands

### Development
```bash
# Install dependencies
pip install -r requirements.txt

# Development server
python run.py --reload --log-level debug

# Production server
python run.py --host 0.0.0.0 --port 8000 --workers 4

# Health check
curl http://127.0.0.1:8000/health
```

### Testing & Quality
```bash
# Run tests (if tests directory exists)
pytest

# Code formatting
black app/
isort app/

# Type checking
mypy app/
```

## Architecture Overview

This is a **FastAPI-based bid/tender data service** with a modular architecture:

### Core Components
- **FastAPI Application** (`app/main.py`): Main app with lifespan management
- **Configuration** (`app/core/config.py`): Pydantic settings with .env support
- **Database**: MySQL + SQLAlchemy ORM + Redis caching
- **Authentication**: JWT-based with role-based access control
- **Scheduling**: APScheduler for background tasks

### Directory Structure
```
app/
├── core/           # Core utilities (config, database, security, middleware)
├── models/         # SQLAlchemy models (user, company, subscribe)
├── routers/        # API endpoints (auth, bid, company, agent, statistics)
├── schemas/        # Pydantic request/response models
├── services/       # Business logic layer
├── tasks/          # Background scheduled tasks
└── utils/          # Helper utilities (cache, API clients)
```

### API Endpoints
- `/api/v1/auth/*` - Authentication
- `/api/v1/bids/*` - Bid/tender data operations
- `/api/v1/company/*` - Company information
- `/api/v1/agent/*` - AI chat and analysis
- `/api/v1/statistics/*` - Analytics and reports
- `/api/v1/subscribe/*` - Subscription management

### Key Services
- **Bid Service**: Integrates with 建安大数据 API for bid data
- **Company Service**: Uses 厦门市信用综合服务平台 for company/patent data
- **Agent Service**: Integrates with HiAgent AI for predictions and analysis
- **Statistics Service**: Provides analytics on bids and companies

### Environment Setup
Required `.env` configuration:
```bash
# API Keys
BID_API_APPID=your_jianan_appid
BID_API_KEY=your_jianan_key
COMPANY_API_KEY_897=your_xiamen_key_897
COMPANY_API_KEY_945=your_xiamen_key_945
COMPANY_API_KEY_943=your_xiamen_key_943
HIAGENT_INQUIRY_KEY=your_hiagent_key
HIAGENT_PROBABILITY_KEY=your_hiagent_prob_key
HIAGENT_KEY_FACTOR_KEY=your_hiagent_factor_key

# Database
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=bid_data

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379

# Email
SENDER_EMAIL=your_email
SENDER_PASSWORD=your_password
SENDER_SMTP_SERVER=smtp.server.com
SENDER_SMTP_PORT=587
```

### Development Notes
- **Auto-reload**: Use `--reload` flag for development
- **Debug mode**: Set `DEBUG=true` in .env for detailed logs
- **Database**: Tables auto-created in debug mode with sample data
- **Health check**: `/health` endpoint provides system metrics
- **API docs**: Available at `/docs` and `/redoc`
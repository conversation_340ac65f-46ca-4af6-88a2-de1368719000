"""
标讯相关的Pydantic模型
"""
from typing import Optional, List
from pydantic import BaseModel, Field


class BidInfo(BaseModel):
    """
    标讯信息的 Pydantic 模型
    """
    area: Optional[str] = None
    buyer: Optional[str] = None
    city: Optional[str] = None
    id: str
    projectname: Optional[str] = None
    publishtime: Optional[int] = None  # 使用 Optional[int] 兼容有些标讯没有发布时间
    subtype: Optional[str] = None
    title: Optional[str] = None
    toptype: Optional[str] = None
    detail: Optional[str] = None
    details: Optional[str] = None
    href: Optional[str] = None
    jybxhref: Optional[str] = None
    budget: Optional[float] = None  # 使用 float 兼容有小数的情况
    buyertel: Optional[str] = None
    buyerperson: Optional[str] = None
    projectcode: Optional[str] = ""
    agency: Optional[str] = None
    bidamount: Optional[float] = None # 使用 float 兼容有小数的情况
    s_winner: Optional[str] = None
    winnerperson: Optional[str] = None
    winnertel: Optional[str] = None
    track_status: Optional[bool] = Field(None, description="是否被跟踪")


class BidDetailResponse(BaseModel):
    """标讯详情响应模型"""
    code: int
    msg: str
    data: Optional[BidInfo] = None


class BidListResponse(BaseModel):
    """标讯列表响应模型"""
    code: int
    msg: str
    data: Optional[List[BidInfo]] = None
    next: Optional[int] = None
    size: Optional[int] = None
    count: Optional[int] = None

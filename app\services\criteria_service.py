from sqlalchemy.orm import Session

from app.crud.crud_criteria import get_criteria_by_user_id, add_criteria, delete_criteria, toggle_track_criteria
from app.schemas.criteria import SearchCriteriaCreate, SearchCriteriaOut
from app.core.exceptions import NotFoundError
from app.core.database import get_db
from app.crud.crud_task import get_track_criteria_task_id
from fastapi import Depends

from app.services.task_service import TaskService, get_task_service


class CriteriaService:
    def __init__(self, db: Session, task_service: TaskService):
        self.db = db
        self.task_service = task_service

    def get_criteria_by_user_id(self, user_id: str) -> list[SearchCriteriaOut]:
        db_criteria_list = get_criteria_by_user_id(self.db, user_id)
        return [SearchCriteriaOut.model_validate(criteria) for criteria in db_criteria_list]

    def add_criteria(self, criteria: SearchCriteriaCreate) -> SearchCriteriaOut:
        db_criteria = add_criteria(self.db, criteria)
        return SearchCriteriaOut.model_validate(db_criteria)

    def delete_criteria(self, criteria_id: str):
        try:
            delete_criteria(self.db, criteria_id)
        except ValueError as e:
            raise NotFoundError(str(e))

    async def toggle_track_criteria(self, criteria_id: str) -> SearchCriteriaOut:
        try:
            db_criteria = toggle_track_criteria(self.db, criteria_id)

            criteria_track_task_id = get_track_criteria_task_id(db_criteria.user_id, db_criteria.id)
            if db_criteria.is_tracked:
                await self.task_service.add_task(
                    func="app.tasks.criteria_track:track_criteria_task",
                    args=[db_criteria.user_id, db_criteria.id],
                    task_id=criteria_track_task_id,
                    trigger="interval",
                    minutes=60,
                )
            else:
                await self.task_service.remove_task(criteria_track_task_id)

            return SearchCriteriaOut.model_validate(db_criteria)
        except ValueError as e:
            raise NotFoundError(str(e))


def get_criteria_service(db: Session = Depends(get_db), task_service: TaskService = Depends(get_task_service)) -> CriteriaService:
    return CriteriaService(db, task_service)
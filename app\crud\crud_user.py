"""
用户、角色、权限相关的 CRUD 操作
"""
from typing import Optional, List
from sqlalchemy.orm import Session, joinedload, selectinload
from sqlalchemy import select

from app.models.user import User, Role, Permission
from app.schemas.user import UserCreate, UserUpdate, RoleCreate, RoleUpdate, PermissionCreate, PermissionUpdate


# --- User CRUD ---

def get_user(db: Session, user_id: str) -> Optional[User]:
    """通过 ID 获取单个用户"""
    return db.get(User, user_id)


def get_user_by_sso_id(db: Session, sso_id: str) -> Optional[User]:
    """通过SSO ID获取单个用户"""
    return db.execute(select(User).filter(User.sso_id == sso_id)).scalar_one_or_none()


def get_user_by_username(db: Session, username: str) -> Optional[User]:
    """通过用户名获取单个用户"""
    return db.execute(select(User).filter(User.username == username)).scalar_one_or_none()


def get_user_with_permissions(db: Session, user_id: str) -> Optional[User]:
    """
    通过 ID 获取用户，并预加载其角色和权限
    """
    return db.execute(
        select(User).options(
            selectinload(User.roles).selectinload(Role.permissions)
        ).filter(User.id == user_id)
    ).scalar_one_or_none()


def get_user_by_email(db: Session, email: str) -> Optional[User]:
    """通过邮箱获取单个用户"""
    return db.execute(select(User).filter(User.email == email)).scalar_one_or_none()


def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[User]:
    """获取用户列表"""
    return list(db.execute(select(User).offset(skip).limit(limit)).scalars().all())


def create_user(db: Session, user: UserCreate, hashed_password: str) -> User:
    """创建新用户"""
    db_user = User(
        username=user.username,
        email=user.email,
        hashed_password=hashed_password
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def update_user(db: Session, db_user: User, user_in: UserUpdate) -> User:
    """更新用户信息"""
    update_data = user_in.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user


def delete_user(db: Session, db_user: User) -> User:
    """删除用户"""
    db.delete(db_user)
    db.commit()
    return db_user


# --- Role CRUD ---

def get_role(db: Session, role_id: int) -> Optional[Role]:
    """通过 ID 获取单个角色"""
    return db.get(Role, role_id)


def create_role(db: Session, role: RoleCreate) -> Role:
    """创建新角色"""
    db_role = Role(name=role.name, description=role.description)
    db.add(db_role)
    db.commit()
    db.refresh(db_role)
    return db_role


# --- Permission CRUD ---

def get_permission(db: Session, permission_id: int) -> Optional[Permission]:
    """通过 ID 获取单个权限"""
    return db.get(Permission, permission_id)


def get_permission_by_name(db: Session, name: str) -> Optional[Permission]:
    """通过名称获取单个权限"""
    return db.execute(select(Permission).filter(Permission.name == name)).scalar_one_or_none()


def create_permission(db: Session, permission: PermissionCreate) -> Permission:
    """创建新权限"""
    db_permission = Permission(name=permission.name, description=permission.description)
    db.add(db_permission)
    db.commit()
    db.refresh(db_permission)
    return db_permission


# --- Association CRUD ---

def add_role_to_user(db: Session, user: User, role: Role) -> User:
    """为用户分配角色"""
    if role not in user.roles:
        user.roles.append(role)
        db.commit()
        db.refresh(user)
    return user


def remove_role_from_user(db: Session, user: User, role: Role) -> User:
    """从用户移除角色"""
    if role in user.roles:
        user.roles.remove(role)
        db.commit()
        db.refresh(user)
    return user


def add_permission_to_role(db: Session, role: Role, permission: Permission) -> Role:
    """为角色分配权限"""
    if permission not in role.permissions:
        role.permissions.append(permission)
        db.commit()
        db.refresh(role)
    return role


def remove_permission_from_role(db: Session, role: Role, permission: Permission) -> Role:
    """从角色移除权限"""
    if permission in role.permissions:
        role.permissions.remove(permission)
        db.commit()
        db.refresh(role)
    return role
from app.core.security import create_access_token, create_refresh_token, decode_token
from app.models.user import User
from app.schemas.token import Token
from app.core.exceptions import InvalidTokenException
from jose import JWTError
from app.crud import crud_user
from sqlalchemy.orm import Session

class AuthService:
    def __init__(self):
        pass

    def sso_login(self, db: Session, sso_token: str) -> Token:
        user = get_user_by_sso_token(db, sso_token)
        if not user:
            raise InvalidTokenException("无效或已过期的Token")
        access_token = create_access_token(user)
        refresh_token = create_refresh_token(user)
        return Token(access_token=access_token, refresh_token=refresh_token)

    def refresh_token(self, db: Session, refresh_token: str) -> Token:
        try:
            token_data = decode_token(refresh_token)
        except JWTError as e:
            raise InvalidTokenException("无效或已过期的Token")
        uid = token_data.sub
        user = crud_user.get_user(db, uid)
        access_token = create_access_token(user)
        return Token(access_token=access_token, refresh_token=refresh_token)


def get_user_by_sso_token(db: Session, sso_token: str) -> User:
    return crud_user.get_user(db, "123")
    raise NotImplementedError("Not implemented")

auth_service = AuthService()

def get_auth_service() -> AuthService:
    return auth_service